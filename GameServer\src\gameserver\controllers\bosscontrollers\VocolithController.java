/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.ai.state.AIState;
import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.ActionObserver.ObserverType;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.services.ItemService;
import gameserver.services.PvpService;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.World;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import javolution.util.FastTable;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class VocolithController extends BossController {
    private static final int[] bossIds = new int[] { 220019, 220020, 235963, 235967, 235969,
        235971, 235972, 235973, 235975 };

    private AtomicBoolean done = new AtomicBoolean(false);
    private AtomicBoolean firstSpawned = new AtomicBoolean(false);

    public VocolithController() {
        super(804573);
    }

    protected void think() {
        // Do nothing
    }

    @Override
    public void onRespawn() {
        super.onRespawn();

        done.set(false);

        if (firstSpawned.compareAndSet(false, true)) {
            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    getOwner().getSpawn().getSpawnGroup().setInterval(Rnd.get(20, 60) * 60);

                    onDelete();
                    scheduleRespawn();
                }
            }, 10 * 1000);
        }
    }

    @Override
    public void onDialogSelect(int dialogId, Player player, int questId) {
        switch (dialogId) {
            case 10000:
                if (!done.compareAndSet(false, true))
                    break;

                getOwner().getSpawn().getSpawnGroup().setInterval(Rnd.get(3, 6) * 60 * 60);

                this.scheduleBossAppearance(getOwner().getWorldId(), getOwner().getInstanceId(),
                    getOwner().getX(), getOwner().getY(), getOwner().getZ());

                super.onDelete();
                super.scheduleRespawn();
                break;
        }
    }

    private void scheduleBossAppearance(final int worldId, final int instanceId, final float x,
        final float y, final float z) {
        spawn(283081, worldId, instanceId, x, y, z, true).delete(8000);

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                int bossId = bossIds[Rnd.get(bossIds.length)];

                final Npc boss = spawn(bossId, worldId, instanceId, x, y, z, true).delete(
                    8 * 60 * 1000);

                boss.getObserveController().addObserver(new ActionObserver(ObserverType.MOVE) {
                    @Override
                    public void moved() {
                        if (!boss.isSpawned())
                            return;

                        SpawnTemplate spawn = boss.getSpawn();

                        if (MathUtil.getDistance(boss, spawn.getX(), spawn.getY(), spawn.getZ()) > 40) {
                            boss.getAi().clearDesires();
                            boss.getAggroList().clearHate();

                            boss.getMoveController().stop();
                            World.getInstance().updatePosition(boss, spawn.getX(), spawn.getY(),
                                spawn.getZ(), spawn.getHeading(), true);
                            boss.getController().stopMoving();

                            boss.setInCombat(false);
                            boss.getLifeStats().increaseHp(TYPE.NATURAL_HP,
                                boss.getLifeStats().getMaxHp() / 6);

                            boss.getAi().setAiState(AIState.ACTIVE);
                        }
                    }
                });

                boss.getObserveController().attach(new ActionObserver(ObserverType.DEATH) {
                    @Override
                    public void died(Creature creature) {
                        final Player killer = creature.getAggroList().getMostPlayerDamage();

                        if (killer != null) {
                            List<Player> attackers = new FastTable<Player>();
                            for (Player pl : creature.getKnownList().getPlayers())
                                if (MathUtil.getDistance(creature, pl) < 40
                                    && pl.getCommonData().getRace() == killer.getCommonData()
                                        .getRace() && !pl.isOutlaw() && !pl.isLawless()
                                    && !pl.isBandit())
                                    attackers.add(pl);

                            int CAPTURE_REWARD_MIGHT = 40 / Math.max(attackers.size(), 2);
                            for (Player attacker : attackers) {
                                PvpService.getInstance().addMight(attacker, CAPTURE_REWARD_MIGHT);
                            }

                            ItemService
                                .addFullItem(killer, 166030005, 2, null, null, null, 0, true);
                            PacketSendUtility
                                .sendMessage(killer,
                                    "You have received two [item:166030005]'s as a reward for fighting off the beast!");
                        }
                    }
                });
            }
        }, 8000);
    }
}

<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	version="1.0" >
	<xs:include schemaLocation="../import.xsd" />
	<xs:include schemaLocation="pvpevent_bosses.xsd" />
	<xs:include schemaLocation="pvpevent_helpers.xsd" />
	<xs:include schemaLocation="pvpevent_spawnpoints.xsd" />
	<xs:element name="pvpevent_data">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="bosses" type="bossgroup" minOccurs="1" maxOccurs="1"/>
				<xs:element name="helpers" type="helpergroup" minOccurs="1" maxOccurs="1"/>
				<xs:element name="spawnpoints" type="point3Dgroup" minOccurs="1" maxOccurs="1"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
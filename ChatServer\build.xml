<?xml version="1.0" encoding="UTF-8"?>
<project name="AX-Chat" default="dist" basedir=".">
	<description>
      This file is part of Aion X Emu [www.aionxemu.com]

         This is free software: you can redistribute it and/or modify
        it under the terms of the GNU Lesser Public License as published by
		the Free Software Foundation, either version 3 of the License, or
		(at your option) any later version.

		This software is distributed in the hope that it will be useful,
		but WITHOUT ANY WARRANTY; without even the implied warranty of
		MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
		GNU Lesser Public License for more details.
		
		You should have received a copy of the GNU Lesser Public License
		along with this software.  If not, see http://www.gnu.org/licenses.
	</description>
	<property name="jre" location="${java.home}/lib"/>
	<property name="src" location="src"/>
	<property name="config" location="config"/>
	<property name="lib" location="lib"/>
	<property name="build" location="build"/>
	<property name="build.classes" location="${build}/classes"/>
	<property name="build.dist" location="${build}/dist"/>
	<property name="build.dist.chat" location="${build.dist}/chatserver"/>
	<path id="bootclasspath">
		<fileset dir="${lib}">
			<include name="jsr166.jar"/>
		</fileset>
		<fileset dir="${jre}">
			<include name="rt.jar"/>
			<include name="jce.jar"/>
		</fileset>
	</path>
	<path id="classpath">
		<fileset dir="${lib}">
			<include name="*.jar"/>
		</fileset>
	</path>
	<target name="clean" description="Removes build directory.">
		<delete dir="${build}"/>
	</target>
	<target name="init" description="Create the output directories.">
		<mkdir dir="${build}"/>
		<mkdir dir="${build.classes}"/>
	</target>
	<target name="compile" depends="init" description="Compile the source.">
		<javac destdir="${build.classes}" optimize="on" debug="on" nowarn="off" source="1.8" target="1.8" includeantruntime="false">
			<src path="${src}"/>
			<bootclasspath refid="bootclasspath"/>
			<classpath refid="classpath"/>
		</javac>
	</target>
	<target name="jar" depends="compile" description="Create the jar file">
		<jar destfile="${build}/ax-chat-1.0.1.jar">
			<fileset dir="${build.classes}"/>
			<manifest>
				<attribute name="Main-Class" value="com.aionengine.chatserver.ChatServer"/>
			</manifest>
		</jar>
	</target>
	<target name="dist" depends="jar">
		<mkdir dir="${build.dist}"/>
		<mkdir dir="${build.dist.chat}"/>
		<mkdir dir="${build.dist.chat}/config"/>
		<mkdir dir="${build.dist.chat}/libs"/>
		<mkdir dir="${build.dist.chat}/log"/>
		<copy todir="${build.dist.chat}">
			<fileset dir="${build}">
				<include name="ax-chat-1.0.1.jar"/>
			</fileset>
		</copy>
		<copy todir="${build.dist.chat}/libs">
			<fileset dir="${lib}">
				<include name="*.jar"/>
			</fileset>
		</copy>
		<copy todir="${build.dist.chat}">
			<fileset dir="dist">
				<include name="*.*"/>
			</fileset>
		</copy>
		<copy todir="${build.dist.chat}/config">
			<fileset dir="config">
				<include name="*.*"/>
			</fileset>
		</copy>
		<zip destfile="${build}/ax_chat.zip" basedir="${build.dist}"/>
	</target>
</project>

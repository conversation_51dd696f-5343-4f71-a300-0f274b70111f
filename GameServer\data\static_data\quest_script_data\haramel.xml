<?xml version="1.0" encoding="utf-8"?>
<quest_scripts>
	<!--
   This file is part of Aion X Emu <aionxemu.com>.

	This is free software: you can redistribute it and/or modify
	it under the terms of the GNU Lesser Public License as published by
	the Free Software Foundation, either version 3 of the License, or
	(at your option) any later version.

	This software is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU Lesser Public License for more details.

	You should have received a copy of the GNU Lesser Public License
	along with this software.  If not, see <http://www.gnu.org/licenses/>.
-->

	<!-- Shugo Indemnity (Elyos Only) -->
	<item_collecting id="18501" start_npc_id="799522" end_npc_id="799523"/>
	<!-- The Maltese Odium (Elyos Only) -->
	<report_to start_npc_id="799523" end_npc_id="798001" id="18503"/>
	<!-- The Third Shugo (Elyos Only) -->
	<report_to start_npc_id="799523" end_npc_id="799524" id="18506"/>
	<!-- A Handshake Deal (Elyos Only) -->
	<monster_hunt start_npc_id="799522" end_npc_id="799523" id="18508">
		<monster_infos var_id="0" npc_id="216898" max_kill="2"/>
		<monster_infos var_id="1" npc_id="216901" max_kill="3"/>
		<monster_infos var_id="2" npc_id="216902" max_kill="3"/>
	</monster_hunt>
	<!-- This Shugo For Hire (Elyos Only) -->
	<item_collecting start_npc_id="799523" end_npc_id="798001" id="18509"/>
	<!-- Out Of The Past (Elyos Only) -->
	<item_collecting start_npc_id="799522" end_npc_id="799523" id="18511"/>
	<!-- The Price Of Loyalty (Asmodian Only) -->
	<item_collecting id="28501" start_npc_id="799522" end_npc_id="799523"/>
	<!-- Odella For Chauminerk (Asmodian Only) -->
	<report_to start_npc_id="799523" end_npc_id="798029" item_id="182212016" id="28503"/>
	<!-- Guess Who (Asmodian Only) -->
	<report_to start_npc_id="799523" end_npc_id="799524" id="28506"/>
	<!-- Securing Moofrenerk's Retreat (Asmodian Only) -->
	<monster_hunt start_npc_id="799522" end_npc_id="799523" id="28508">
		<monster_infos var_id="0" npc_id="216898" max_kill="2"/>
		<monster_infos var_id="1" npc_id="216901" max_kill="3"/>
		<monster_infos var_id="2" npc_id="216902" max_kill="3"/>
	</monster_hunt>
	<!-- What is Inside the Box? (Asmodian Only) -->
	<item_collecting start_npc_id="799523" end_npc_id="798029" id="28509"/>
	<!-- The Soup? Nutsy! (Asmodian Only) -->
	<item_collecting start_npc_id="799522" end_npc_id="799523" id="28511"/>
</quest_scripts>
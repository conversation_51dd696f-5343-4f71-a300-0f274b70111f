/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.ChatType;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.RequestResponseHandler;
import gameserver.network.aion.serverpackets.SM_LEVEL_UPDATE;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_QUESTION_WINDOW;
import gameserver.services.ItemService;
import gameserver.utils.PacketSendUtility;

import java.util.Arrays;

/**
 * <AUTHOR>
 * 
 */
public class PumpkinPatrolController extends BossController {

    public PumpkinPatrolController() {
        super(Arrays.asList(730103, 730104), true);
    }

    @Override
    public void onCreation() {
        Npc owner = getOwner();

        owner.setCustomName("Pumpkin Patrol");
        owner.setCustomTag("Halloween Event");
    }

    @Override
    protected void think() {

    }

    @Override
    public void onDialogRequest(Player player) {
        message(player, "Click Yes to trade in your [item:186000169]'s.");
        request(player);
    }

    private void request(Player player) {
        RequestResponseHandler responseHandler = new RequestResponseHandler(getOwner()) {
            @Override
            public void acceptRequest(Creature requester, Player responder) {
                if (responder.getInventory().getItemCountByItemId(186000169) < 5) {
                    message(responder, "You need at least 5 x [item: 186000169] to trade.");
                    return;
                }

                int count = 0;

                while (responder.getInventory().getItemCountByItemId(186000169) >= 5) {
                    responder.getInventory().removeFromBagByItemId(186000169, 5);

                    ItemService.addItem(responder, 186000389, 1);

                    count++;
                }

                message(responder, "You have received " + count + " [item:186000389]'s!");

                PacketSendUtility.broadcastPacketAndReceive(responder, new SM_LEVEL_UPDATE(
                    responder.getObjectId(), 4, responder.getLevel()));
            }

            @Override
            public void denyRequest(Creature requester, Player responder) {
            }
        };

        boolean requested = player.getResponseRequester().putRequest(SM_QUESTION_WINDOW.STR_CUSTOM,
            responseHandler);
        if (requested) {
            PacketSendUtility.sendPacket(player, new SM_QUESTION_WINDOW(
                SM_QUESTION_WINDOW.STR_CUSTOM, 0,
                "Do you wish to trade in your Pumpkin Cookies for Arcade Tokens?", ""));
        }
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendPacket(player, new SM_MESSAGE(getOwner().getObjectId(),
            "Pumpkin Patrol", msg, ChatType.ALLIANCE));
    }
}

/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.Creature;

/**
 * <AUTHOR>
 */
public class KaluvaController extends BossController {
    private final BossSkill AGONIZING_CRAMPS = new BossSkill(19154, 10);
    private final BossSkill BINDING_WEB = new BossSkill(19157, 10);
    private final BossSkill AETHERIC_TURBULENCE = new BossSkill(19156, 10);
    private final BossSkill DIVINE_TOUCH = new BossSkill(19170, 1);
    private final BossSkill UNSTABLE_EARTH = new BossSkill(19176, 1);

    private final int FLASH_LAPILIMO = 281896;
    private final int NOBLE_LAPILIMA = 216946;

    private Mode mode = Mode.WAITING;

    private int addWavesDone = 0;

    public KaluvaController() {
        super(216950, true);
    }

    @Override
    protected void think() {
        Creature owner = getOwner();

        int hp = owner.getLifeStats().getHpPercentage();
        if (addWavesDone < 1 && hp <= 90) {
            spawnAdds(FLASH_LAPILIMO, 2);
            addWavesDone++;
        }
        else if (addWavesDone < 2 && hp <= 75) {
            spawnAdds(NOBLE_LAPILIMA, 3);
            addWavesDone++;
        }
        else if (addWavesDone < 3 && hp <= 50) {
            AGONIZING_CRAMPS.use(getPriorityTarget());
            spawnAdds(FLASH_LAPILIMO, 3);
            addWavesDone++;
        }
        else if (addWavesDone < 4 && hp <= 25) {
            spawnAdds(FLASH_LAPILIMO, 2);
            spawnAdds(NOBLE_LAPILIMA, 2);
            addWavesDone++;
        }

        switch (mode) {
            case WAITING:
                if (owner.getAggroList().getMostHated() != null)
                    mode = Mode.ACTIVE;
                break;

            case ACTIVE:
                if (owner.getLifeStats().getHpPercentage() < 50) {
                    mode = Mode.ENRAGED;
                    break;
                }

                if (DIVINE_TOUCH.timeSinceUse() > 20)
                    DIVINE_TOUCH.use(getPriorityTarget());
                else if (BINDING_WEB.timeSinceUse() > 15)
                    BINDING_WEB.use(getRandomTarget());
                else if (AETHERIC_TURBULENCE.timeSinceUse() > 12)
                    AETHERIC_TURBULENCE.use(getMostHated());
                break;
            case ENRAGED:
                getOwner().getAggroList().addHate(getPriorityTarget(), 5000);

                if (UNSTABLE_EARTH.timeSinceUse() > 20)
                    UNSTABLE_EARTH.use(getPriorityTarget());
                else if (BINDING_WEB.timeSinceUse() > 12)
                    BINDING_WEB.use(getRandomTarget());
                else if (DIVINE_TOUCH.timeSinceUse() > 10)
                    DIVINE_TOUCH.use(getPriorityTarget());
                else if (AETHERIC_TURBULENCE.timeSinceUse() > 8)
                    AETHERIC_TURBULENCE.use(getMostHated());
                break;
        }
    }

    enum Mode {
        WAITING,
        ACTIVE,
        ENRAGED
    }
}
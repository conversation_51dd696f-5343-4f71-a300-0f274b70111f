/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.attack;

import gameserver.configs.main.CustomConfig;
import gameserver.controllers.MoveController.MoveDirection;
import gameserver.dataholders.DataManager;
import gameserver.model.SkillElement;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.NpcWithCreator;
import gameserver.model.gameobjects.Servant;
import gameserver.model.gameobjects.Summon;
import gameserver.model.gameobjects.Trap;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.CreatureGameStats;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.templates.item.WeaponType;
import gameserver.network.aion.serverpackets.SM_LOOKATOBJECT;
import gameserver.network.aion.serverpackets.SM_TARGET_SELECTED;
import gameserver.network.aion.serverpackets.SM_TARGET_UPDATE;
import gameserver.skillengine.action.DamageType;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.ForceMoveParam;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.taskmanager.tasks.PacketBroadcaster;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.stats.StatFunctions;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 *         <p/>
 *         Probably this is a temporary class for attack calculation cause i need it during refactoring
 */
public class AttackUtil {

    /**
     * @param attacker
     * @param attacked
     * @return List<AttackResult>
     */
    public static List<AttackResult> calculatePhysicalAttackResult(Creature attacker,
        Creature attacked, int time) {
        List<AttackResult> attackList = new ArrayList<AttackResult>();

        float damageMultiplier = attacker.getObserveController().getBasePhysicalDamageMultiplier(
            false);
        CreatureGameStats<?> gameStats = attacker.getGameStats();

        // calculate damage
        int damage = StatFunctions.calculateBaseDamageToTarget(attacker, attacked);

        // attacked status
        AttackStatus status = calculatePhysicalStatus(attacker, attacked);
        switch (status) {
            case DODGE:
                attacked.setDodgeTimer();
                break;
            case BLOCK:
                attacked.setBlockTimer();
                int shieldDamageReduce = attacked.getGameStats().getCurrentStat(
                    StatEnum.DAMAGE_REDUCE);
                damage -= Math.round((damage * shieldDamageReduce) / 100);
                break;
            case PARRY:
                attacked.setParryTimer();
                damage *= 0.6;
                break;
        }

        // attacker status
        int criticalReduce = attacked.getGameStats().getCurrentStat(
            StatEnum.PHYSICAL_CRITICAL_DAMAGE_REDUCE);
        if (calculatePhysicalAttackerStatus(attacker, attacked, 1, false) == AttackStatus.CRITICAL) {
            switch (status) {
                case DODGE:
                    status = AttackStatus.CRITICAL_DODGE;
                    break;
                case BLOCK:
                    status = AttackStatus.CRITICAL_BLOCK;
                    break;
                case PARRY:
                    status = AttackStatus.CRITICAL_PARRY;
                    break;
                default:
                    status = AttackStatus.CRITICAL;
                    break;
            }

            WeaponType weaponType = null;
            if (attacker instanceof Player)
                weaponType = ((Player) attacker).getEquipment().getMainHandWeaponType();

            if (weaponType != null)
                damage = calculateWeaponCritical(damage, criticalReduce, weaponType, true);
        }

        damage = Math.round(damage * damageMultiplier);

        // adjust damage
        damage = adjustDamages(attacker, attacked, damage, false, false);

        switch (status) {
            case DODGE:
            case CRITICAL_DODGE:
                damage = 0;
                break;
            default:
                if (damage <= 0)
                    damage = 1;
                break;
        }

        int hitCount = Rnd.get(1, gameStats.getCurrentStat(StatEnum.MAIN_HAND_HITS));

        attackList.addAll(splitDamage(hitCount, damage, status));

        // calculate offhand damage
        if (attacker instanceof Player
            && ((Player) attacker).getEquipment().getOffHandWeaponType() != null) {
            int offHandDamage = StatFunctions.calculateOffHandPhysicDamageToTarget(attacker,
                attacked);

            AttackStatus offHandStatus;
            switch (status) {
                case DODGE:
                    offHandStatus = AttackStatus.OFFHAND_DODGE;
                    break;
                case CRITICAL_DODGE:
                    offHandStatus = AttackStatus.OFFHAND_CRITICAL_DODGE;
                    break;
                case BLOCK:
                    offHandStatus = AttackStatus.OFFHAND_BLOCK;
                    break;
                case CRITICAL_BLOCK:
                    offHandStatus = AttackStatus.OFFHAND_CRITICAL_BLOCK;
                    break;
                case PARRY:
                    offHandStatus = AttackStatus.OFFHAND_PARRY;
                    break;
                case CRITICAL_PARRY:
                    offHandStatus = AttackStatus.OFFHAND_CRITICAL_PARRY;
                    break;
                case CRITICAL:
                    offHandStatus = AttackStatus.OFFHAND_CRITICAL;
                    break;
                default:
                    offHandStatus = AttackStatus.OFFHAND_NORMALHIT;
                    break;
            }

            offHandDamage = calculateOffHandResult(attacker, attacked, offHandDamage, offHandStatus);

            // multiplier
            offHandDamage = Math.round(offHandDamage * damageMultiplier);

            offHandDamage = adjustDamages(attacker, attacked, offHandDamage, false, false);

            switch (offHandStatus) {
                case OFFHAND_DODGE:
                case OFFHAND_CRITICAL_DODGE:
                    offHandDamage = 0;
                    break;
                default:
                    if (offHandDamage <= 0)
                        offHandDamage = 1;
                    break;
            }

            int offHandHits = Rnd.get(1, gameStats.getCurrentStat(StatEnum.OFF_HAND_HITS));
            attackList.addAll(splitDamage(offHandHits, offHandDamage, offHandStatus));
        }

        // check for shield
        attacked.getObserveController().checkShieldStatus(attackList, attacker, null);

        // effect on critical hit
        if (attacker instanceof Player && attackList.size() > 0
            && attackList.get(0).getDamage() > 0)
            if (launchEffectOnHit((Player) attacker, attacked, (status == AttackStatus.CRITICAL
                || status == AttackStatus.CRITICAL_BLOCK || status == AttackStatus.CRITICAL_PARRY),
                time, false) != 0)
                attackList.add(0, new AttackResult(0, AttackStatus.STUMBLE_OR_STAGGER));

        return attackList;
    }

    /**
     * used to calculate offhand attack
     * 
     * @param attacker
     * @param attacked
     * @param damage
     * @param status
     * @return
     */
    private static int calculateOffHandResult(Creature attacker, Creature attacked, int damage,
        AttackStatus status) {
        switch (status) {
            case OFFHAND_DODGE:
            case OFFHAND_CRITICAL_DODGE:
                damage = 0;
                break;
            case OFFHAND_BLOCK:
            case OFFHAND_CRITICAL_BLOCK:
                int shieldDamageReduce = attacked.getGameStats().getCurrentStat(
                    StatEnum.DAMAGE_REDUCE);
                damage -= Math.round((damage * shieldDamageReduce) / 100);
                break;
            case OFFHAND_PARRY:
            case OFFHAND_CRITICAL_PARRY:
                damage *= 0.6;
                break;
        }

        // effector status
        int criticalReduce = attacked.getGameStats().getCurrentStat(
            StatEnum.PHYSICAL_CRITICAL_DAMAGE_REDUCE);
        switch (status) {
            case OFFHAND_CRITICAL_DODGE:
            case OFFHAND_CRITICAL_BLOCK:
            case OFFHAND_CRITICAL_PARRY:
            case OFFHAND_CRITICAL:
                WeaponType weaponType = ((Player) attacker).getEquipment().getOffHandWeaponType();
                damage = calculateWeaponCritical(damage, criticalReduce, weaponType, false);
                break;
        }

        return damage;
    }

    public static List<AttackResult> calculateMagicalAttackResult(Creature attacker,
        Creature attacked) {
        List<AttackResult> attackList = new ArrayList<AttackResult>();

        float damageMultiplier = attacker.getObserveController().getBaseMagicalDamageMultiplier(
            false);

        SkillElement element = attacker.getAttackType().getElement();

        int damage = StatFunctions.calculateMagicalAttackToTarget(attacker, attacked, element);

        // calculate status
        AttackStatus status = calculateMagicalStatus(attacker, attacked);

        if (status == AttackStatus.CRITICAL) {
            float criticalReduce = (float) attacked.getGameStats().getCurrentStat(
                StatEnum.MAGICAL_CRITICAL_DAMAGE_REDUCE);
            damage *= 1.5f;
            damage = Math.round(damage - (damage * criticalReduce / 1000f));
        }

        // adjusting baseDamages according to attacker and target level
        damage = adjustDamages(attacker, attacked, damage, false, true);

        // damage multiplier
        damage = Math.round(damage * damageMultiplier);

        if (damage <= 0)
            damage = 1;

        switch (status) {
            case RESIST:
            case MAGICAL_CRITICAL_RESIST:
                attacked.setResistTimer();
                damage = 0;
                break;
        }

        attackList.add(new AttackResult(damage, status, 0, 0, DamageType.MAGICAL));

        // calculate offhand damage
        if (attacker instanceof Player
            && ((Player) attacker).getEquipment().getOffHandWeaponType() != null) {
            int offHandDamage = StatFunctions.calculateOffHandMagicalAttackToTarget(attacker,
                attacked, element);

            AttackStatus offHandStatus;
            switch (status) {
                case RESIST:
                    offHandStatus = AttackStatus.OFFHAND_RESIST;
                    break;
                case CRITICAL:
                    offHandStatus = AttackStatus.OFFHAND_CRITICAL;
                    break;
                default:
                    offHandStatus = AttackStatus.OFFHAND_NORMALHIT;
                    break;
            }

            if (offHandStatus == AttackStatus.OFFHAND_CRITICAL) {
                float criticalReduce = (float) attacked.getGameStats().getCurrentStat(
                    StatEnum.MAGICAL_CRITICAL_DAMAGE_REDUCE);
                offHandDamage *= 1.5f;
                offHandDamage = Math
                    .round(offHandDamage - (offHandDamage * criticalReduce / 1000f));
            }

            offHandDamage = adjustDamages(attacker, attacked, offHandDamage, false, true);
            offHandDamage = Math.round(offHandDamage * damageMultiplier);

            if (offHandDamage <= 0)
                offHandDamage = 1;

            switch (offHandStatus) {
                case OFFHAND_RESIST:
                case OFFHAND_CRITICAL_RESIST:
                    offHandDamage = 0;
                    break;
            }

            attackList
                .add(new AttackResult(offHandDamage, offHandStatus, 0, 0, DamageType.MAGICAL));
        }

        // check for shield
        attacked.getObserveController().checkShieldStatus(attackList, attacker, null);

        return attackList;
    }

    public static List<AttackResult> splitDamage(int hitCount, int damage, AttackStatus status) {
        List<AttackResult> attackList = new ArrayList<AttackResult>();

        for (int i = 0; i < hitCount; i++) {
            int damages = damage;

            if (i != 0) {
                damages = Math.round(damage * 0.1f);
            }

            attackList.add(new AttackResult(damages, status));
        }
        return attackList;
    }

    public static List<AttackResult> splitPhysicalDamage(Creature attacker, Creature attacked,
        int hitCount, int damage, AttackStatus status) {
        List<AttackResult> attackList = new ArrayList<AttackResult>();

        for (int i = 0; i < hitCount; i++) {
            int damages = damage;

            if (i != 0) {
                damages = Math.round(damage * 0.1f);
            }

            WeaponType weaponType;

            // TODO this is very basic calcs, for initial testing only
            switch (status) {
                case BLOCK:
                case OFFHAND_BLOCK:
                    int shieldDamageReduce = ((Player) attacked).getGameStats().getCurrentStat(
                        StatEnum.DAMAGE_REDUCE);
                    damages -= Math.round((damages * shieldDamageReduce) / 100f);
                    break;

                case DODGE:
                case OFFHAND_DODGE:
                    damages = 0;
                    break;

                case CRITICAL:
                    weaponType = ((Player) attacker).getEquipment().getMainHandWeaponType();
                    damages = calculateWeaponCritical(damages, attacked.getGameStats()
                        .getCurrentStat(StatEnum.PHYSICAL_CRITICAL_DAMAGE_REDUCE), weaponType, true);
                    break;

                case OFFHAND_CRITICAL:
                    weaponType = ((Player) attacker).getEquipment().getOffHandWeaponType();
                    damages = calculateWeaponCritical(damages, attacked.getGameStats()
                        .getCurrentStat(StatEnum.PHYSICAL_CRITICAL_DAMAGE_REDUCE), weaponType,
                        false);
                    break;

                case PARRY:
                case OFFHAND_PARRY:
                    damages *= 0.6;
                    break;

                default:
                    break;
            }
            attackList.add(new AttackResult(damages, status));
        }
        return attackList;
    }

    /**
     * [Critical] Spear : x1.8 Sword : x2.2 Dagger : x2.3 Mace : x2.0 Greatsword : x1.8 Orb : x1.5 Spellbook : x1.5 Bow
     * : x1.7 Staff : x1.7
     * 
     * @param damages
     * @param weaponType
     * @return
     */
    private static int calculateWeaponCritical(int damages, int criticalReduce,
        WeaponType weaponType, boolean main) {
        float critModifier = 1.5f;
        switch (weaponType) {
            case DAGGER_1H:
                critModifier = 2.3f;
                break;
            case SWORD_1H:
                critModifier = 2.2f;
                break;
            case MACE_1H:
                critModifier = 2.0f;
                break;
            case SWORD_2H:
            case POLEARM_2H:
                critModifier = 1.8f;
                break;
            case STAFF_2H:
            case BOW:
                critModifier = 1.7f;
                break;
            case GUN_1H: // [TODO]
            case CANNON_2H: // [TODO]
            case HARP_2H: // [TODO]
            case KEYBLADE_2H: // [TODO]
            case KEYHAMMER: // [TODO]
                break;
        }

        if (!main)
            criticalReduce = -criticalReduce;

        damages = Math.round(damages * (critModifier - criticalReduce * 0.001f));

        return damages;
    }

    public static AttackStatus calculateSkillPhysicalStatus(Creature attacker, Creature attacked) {
        // check always block
        if (attacked.getObserveController().checkAttackStatus(AttackStatus.BLOCK)
            && attacked instanceof Player && ((Player) attacked).getEquipment().isShieldEquipped())
            return AttackStatus.BLOCK;

        if (attacked instanceof Player && ((Player) attacked).getEquipment().isShieldEquipped()
            && Rnd.get(0, 100) < StatFunctions.calculatePhysicalBlockRate(attacker, attacked))
            return AttackStatus.BLOCK;

        if (attacked instanceof Player
            && ((Player) attacked).getEquipment().getMainHandWeaponType() != null
            && Rnd.get(0, 100) < StatFunctions.calculatePhysicalParryRate(attacker, attacked))
            return AttackStatus.PARRY;

        return AttackStatus.NORMALHIT;
    }

    public static AttackStatus calculatePhysicalAttackerStatus(Creature attacker,
        Creature attacked, float criticalProb, boolean skill) {
        if (attacker instanceof Player
            && ((Player) attacker).getEquipment().getMainHandWeaponType() != null
            && Rnd.get(0, 100) < StatFunctions.calculatePhysicalCriticalRate(attacker, attacked,
                criticalProb, skill))
            return AttackStatus.CRITICAL;

        return AttackStatus.NORMALHIT;
    }

    /**
     * 
     * @param effect
     * @param skillDamage
     */
    public static void calculatePhysicalSkillAttackResult(Effect effect, int skillDamage,
        int bonusDamage, int rng, int time) {
        if (effect.getReserved1() > 0)
            return;

        Creature effector = effect.getEffector();
        Creature effected = effect.getEffected();

        int damage = StatFunctions.calculatePhysicDamageToTarget(effector, effected, skillDamage,
            bonusDamage);

        // effected status
        AttackStatus status = ((effect.getAccuracyMode() & 1) == 1 || (effect.getAccuracyMode() & 2) == 2) ? AttackStatus.NORMALHIT
            : calculateSkillPhysicalStatus(effector, effected);
        switch (status) {
            case BLOCK:
                effect.getEffected().setBlockTimer();
                int shieldDamageReduce = ((Player) effected).getGameStats().getCurrentStat(
                    StatEnum.DAMAGE_REDUCE);
                damage -= Math.round((damage * shieldDamageReduce) / 100);
                break;
            case PARRY:
                effect.getEffected().setParryTimer();
                damage *= 0.6;
                break;
            default:
                break;
        }

        // effector status
        int criticalReduce = effect.getEffected().getGameStats()
            .getCurrentStat(StatEnum.PHYSICAL_CRITICAL_DAMAGE_REDUCE);
        if (calculatePhysicalAttackerStatus(effector, effected, effect.getCriticalProb(), true) == AttackStatus.CRITICAL) {
            switch (status) {
                case BLOCK:
                    status = AttackStatus.CRITICAL_BLOCK;
                    break;
                case PARRY:
                    status = AttackStatus.CRITICAL_PARRY;
                    break;
                default:
                    status = AttackStatus.CRITICAL;
                    break;
            }

            if (effector instanceof Player)
                damage = calculateWeaponCritical(damage, criticalReduce, ((Player) effector)
                    .getEquipment().getMainHandWeaponType(), true);
            else
                damage *= 2;
        }

        // multiply damage
        float damageMultiplier = effector.getObserveController().getBasePhysicalDamageMultiplier(
            true);
        damage *= damageMultiplier;

        // adjust damamge
        damage = adjustDamages(effector, effected, damage, false, false);

        // pvp damage
        if (effect.getEffected() instanceof Player && effect.getPvpDamage() != 0)
            damage = Math.round(damage * (effect.getPvpDamage() / 100f));

        // implementation of random damage for skills like Stunning Shot, etc
        if (rng > 0) {
            int randomChance = Rnd.get(100);

            switch (rng) {
                case 1:
                    if (randomChance < 40)
                        damage *= 1.5;
                    else if (randomChance < 70)
                        damage /= 2;
                    break;
                case 2:
                    // if (randomChance <= 20)
                    // damage *= 3;
                    if (randomChance < 20)
                        damage *= 2;
                    else
                        damage /= 2;
                    break;
                case 3:
                    // if (randomChance <= 15)
                    // damage *= 1.5f;
                    // damage *= 1 + Rnd.get(15, 35) * 0.01f;
                    break;
                case 6:
                    if (randomChance < 30)
                        damage *= 2f;
                    else
                        damage *= 1f;
                    break;
                // TODO rest of the cases
                default:
                    // Logger.getLogger(AttackUtil.class).debug(
                    // "Missing handled rng: " + rng + " for skillId: " + effect.getSkillId());
                    break;
            }
        }

        if (damage <= 0)
            damage = 1;

        calculateEffectResult(effect, effected, damage, status);
    }

    /**
     * If attacker is blinded - return DODGE for physical attacks
     * 
     * @param effector
     * @return
     */
    private static AttackStatus calculateAttackerPhysicalStatus(Creature effector) {
        if (effector.getObserveController().checkAttackerStatus(AttackStatus.DODGE))
            return AttackStatus.DODGE;
        return null;
    }

    /**
     * adjust baseDamages according to their level || is PVP?
     * 
     * @ref:
     * 
     * @param attacker
     *            lvl
     * @param target
     *            lvl
     * @param baseDamages
     * 
     **/
    public static int adjustDamages(Creature attacker, Creature target, int damages, boolean isDot,
        boolean isMagical) {
        int attackerLevel = attacker.getLevel();
        int targetLevel = target.getLevel();
        int baseDamages = damages;

        // fix this for better monster target condition please
        if ((attacker instanceof Player || attacker instanceof Servant) && target instanceof Npc
            && !(target instanceof Servant)) {
            float pveAttackBonus = attacker.getGameStats()
                .getCurrentStat(StatEnum.PVE_ATTACK_RATIO) * 0.001f;
            float pveDefenceBonus = target.getGameStats().getCurrentStat(StatEnum.PVE_DEFEND_RATIO) * 0.001f;

            baseDamages = Math.round(baseDamages * (1 + pveAttackBonus - pveDefenceBonus));

            if (targetLevel > attackerLevel) {
                float multipler = 0.0f;
                int differ = (targetLevel - attackerLevel);

                if (differ > 2 && differ < 10) {
                    multipler = (differ - 2f) / 10f;
                    baseDamages -= Math.round((baseDamages * multipler));
                }
                else if (differ >= 10)
                    baseDamages -= Math.round((baseDamages * 0.80f));
            }
        } // end of damage to monster
        else if ((attacker instanceof Player || attacker instanceof NpcWithCreator
            || attacker instanceof Servant || attacker instanceof Summon)
            && (target instanceof Player || target instanceof Summon || target instanceof Servant)
            && (!(attacker instanceof Trap) || !isDot)) {
            baseDamages = Math.round(baseDamages * getGlobalPvPDamageModifier());
            float pvpAttackBonus = attacker.getGameStats()
                .getCurrentStat(StatEnum.PVP_ATTACK_RATIO) * 0.001f;
            float pvpDefenceBonus = target.getGameStats().getCurrentStat(StatEnum.PVP_DEFEND_RATIO) * 0.001f;

            if (isMagical) {
                pvpAttackBonus += attacker.getGameStats().getCurrentStat(
                    StatEnum.PVP_ATTACK_RATIO_MAGICAL) * 0.001f;
                pvpDefenceBonus += target.getGameStats().getCurrentStat(
                    StatEnum.PVP_DEFEND_RATIO_MAGICAL) * 0.001f;
            }
            else {
                pvpAttackBonus += attacker.getGameStats().getCurrentStat(
                    StatEnum.PVP_ATTACK_RATIO_PHYSICAL) * 0.001f;
                pvpDefenceBonus += target.getGameStats().getCurrentStat(
                    StatEnum.PVP_DEFEND_RATIO_PHYSICAL) * 0.001f;
            }

            baseDamages = Math.round(baseDamages * (1 + pvpAttackBonus - pvpDefenceBonus));
        }

        if (!isDot) {
            // Movement damage modifiers
            if (attacker instanceof Player) {
                if (attacker.getMoveController().hasMoveDirection(MoveDirection.FORWARD)) {
                    baseDamages = Math.round(baseDamages * 1.1f);
                }

                if (attacker.getMoveController().hasMoveDirection(MoveDirection.BACKWARD)
                    || attacker.getMoveController().hasMoveDirection(MoveDirection.STRAFE)) {
                    if (!isMagical
                        && !(attacker.isCasting() && attacker.getCastingSkill().isMagical()))
                        baseDamages = Math.round(baseDamages * 0.75f); // Physical reduction
                    else
                        baseDamages = Math.round(baseDamages * 0.80f); // Magical reduction
                }
            }
        }

        return baseDamages;
    }

    private static float getGlobalPvPDamageModifier() {
        if (CustomConfig.OLD_SCHOOL)
            return 0.50f;

        return 0.42f;
    }

    /**
     * calculate damage for damage over time effects(bleed,poison,spellatk)
     * 
     * @param effect
     * @param skillDamage
     * @param element
     * @return
     */

    public static int calculateMagicalOverTimeResult(Effect effect, int skillDamage,
        SkillElement element, int position, boolean canCrit) {
        Creature effector = effect.getEffector();
        Creature effected = effect.getEffected();

        // float damageMultiplier = effector.getObserveController().getBaseMagicalDamageMultiplier(
        // false);
        int damage = StatFunctions.calculateMagicDamageToTarget(effector, effected, skillDamage, 0,
            element, true); // TODO
        // SkillElement

        float criticalReduce = (float) effect.getEffected().getGameStats()
            .getCurrentStat(StatEnum.MAGICAL_CRITICAL_DAMAGE_REDUCE);

        AttackStatus status = AttackStatus.NORMALHIT;

        if (position == 1 || canCrit)
            status = calculateMagicalEffectorStatus(effect);
        else
            status = effect.getAttackStatus();

        if (status == AttackStatus.CRITICAL) {
            damage *= 1.5f;
            damage = Math.round(damage - (damage * criticalReduce / 1000f));
        }

        // adjusting baseDamages according to attacker and target level
        // damage = adjustDamages(effector, effected, damage, true, true);

        // no damage multiplier
        // damage = Math.round(damage * damageMultiplier);

        // pvp damage
        if (effect.getEffected() instanceof Player && effect.getPvpDamage() != 0)
            damage = Math.round(damage * (effect.getPvpDamage() / 100f));

        if (damage <= 0)
            damage = 1;

        return damage;
    }

    /**
     * @param effect
     * @param effected
     * @param damage
     * @param status
     */
    public static void calculateEffectResult(Effect effect, Creature effected, int damage,
        AttackStatus status) {
        AttackResult attackResult = new AttackResult(damage, status, 0, 0, effect.getDamageType());

        if (!effect.isDelayDamage())
            effected.getObserveController().checkShieldStatus(
                Collections.singletonList(attackResult), effect.getEffector(), effect);

        effect.setReserved1(attackResult.getDamage());
        effect.setAttackStatus(attackResult.getAttackStatus());
        effect.setShieldType(attackResult.getShieldType());
        effect.setProtectorId(attackResult.getProtectorId());
        effect.setProtectedDamage(attackResult.getProtectedDamage());
        effect.setProtectedSkillId(attackResult.getProtectedSkillId());
        effect.setReflectedDamage(attackResult.getReflectedDamage());
        effect.setReflectedSkillId(attackResult.getReflectedSkillId());
        effect.setManaShieldCost(attackResult.getManaShieldCost());
        effect.setManaShieldSkillId(attackResult.getManaShieldSkillId());
    }

    public static AttackStatus calculateMagicalEffectorStatus(Effect effect) {
        if (Rnd.get(0, 100) < StatFunctions.calculateMagicalCriticalRate(effect.getEffector(),
            effect.getEffected(), effect.getCriticalProb()))
            return AttackStatus.CRITICAL;
        else
            return AttackStatus.NORMALHIT;
    }

    /**
     * 
     * @param effect
     * @param skillDamage
     * @param element
     */
    public static void calculateMagicalSkillAttackResult(Effect effect, int skillDamage,
        SkillElement element, int bonusDamage, boolean applyKnowledge) {
        Creature effector = effect.getEffector();
        Creature effected = effect.getEffected();

        float damageMultiplier = 1.0f;
        if (!effect.getSkillTemplate().isProvoked())
            damageMultiplier = effector.getObserveController().getBaseMagicalDamageMultiplier(true);

        int damage = StatFunctions.calculateMagicDamageToTarget(effector, effected, skillDamage,
            bonusDamage, element, !applyKnowledge);

        float criticalReduce = (float) effect.getEffected().getGameStats()
            .getCurrentStat(StatEnum.MAGICAL_CRITICAL_DAMAGE_REDUCE);
        AttackStatus status = calculateMagicalEffectorStatus(effect);
        if (status == AttackStatus.CRITICAL) {
            if (effect.getStack().equals("WI_MAGICALCHAIN"))
                damage *= 3.0f;
            else
                damage *= 1.5f;

            damage = Math.round(damage - (damage * criticalReduce / 1000f));
        }

        // adjusting baseDamages according to attacker and target level
        damage = adjustDamages(effector, effected, damage, false, true);

        // damage multiplier
        damage = Math.round(damage * damageMultiplier);

        // pvp damage
        if (effect.getEffected() instanceof Player && effect.getPvpDamage() != 0)
            damage = Math.round(damage * (effect.getPvpDamage() / 100f));

        if (damage <= 0)
            damage = 1;

        calculateEffectResult(effect, effected, damage, status);
    }

    /**
     * Manage attack status rate
     * 
     * @return AttackStatus
     * @source 
     *         http://www.aionsource.com/forum/mechanic-analysis/42597-character-stats-xp-dp-origin-gerbator-team-july-2009
     *         -a.html
     */
    public static AttackStatus calculatePhysicalStatus(Creature attacker, Creature attacked) {
        if (Rnd.get(0, 100) < StatFunctions.calculatePhysicalDodgeRate(attacker, attacked, 0))
            return AttackStatus.DODGE;

        // check always block
        if (attacked.getObserveController().checkAttackStatus(AttackStatus.BLOCK)
            && attacked instanceof Player && ((Player) attacked).getEquipment().isShieldEquipped())
            return AttackStatus.BLOCK;

        if (attacked instanceof Player && ((Player) attacked).getEquipment().isShieldEquipped()
            && Rnd.get(0, 100) < StatFunctions.calculatePhysicalBlockRate(attacker, attacked))
            return AttackStatus.BLOCK;

        if (attacked instanceof Player
            && ((Player) attacked).getEquipment().getMainHandWeaponType() != null
            && Rnd.get(0, 100) < StatFunctions.calculatePhysicalParryRate(attacker, attacked))
            return AttackStatus.PARRY;

        return AttackStatus.NORMALHIT;
    }

    public static AttackStatus calculateMagicalStatus(Creature attacker, Creature attacked) {
        if (Rnd.get(0, 100) < StatFunctions.calculateMagicalResistRate(attacker, attacked, 0, 0))
            return AttackStatus.RESIST;

        if (Rnd.get(0, 100) < StatFunctions.calculateMagicCriticalRate(attacker, attacked))
            return AttackStatus.CRITICAL;

        return AttackStatus.NORMALHIT;
    }

    public static int launchEffectOnHit(final Player attacker, final Creature attacked,
        boolean crit, int time, boolean skill) {
        if (attacker.getEquipment() == null
            || attacker.getEquipment().getMainHandWeaponType() == null)
            return 0;

        int skillId = 0;

        switch (attacker.getEquipment().getMainHandWeaponType()) {
            case POLEARM_2H:
            case STAFF_2H:
            case SWORD_2H:
                skillId = 8218;
                break;
            case BOW:
                skillId = 8217;
                break;
        }

        if (skillId == 0)
            return 0;

        if (crit) {
            if (Rnd.get(100) >= 10) // hardcoded 10% chance
                return 0;
        }
        else {
            // if (Rnd.get(100) >= 1) // hardcoded 1% chance
            return 0;
        }

        final SkillTemplate template = DataManager.SKILL_DATA.getSkillTemplate(skillId);
        if (template == null)
            return 0;

        if (skill) {
            return skillId;
        }
        else {
            final Effect e = new Effect(attacker, attacked, template, template.getLvl(),
                template.getEffectsDuration());
            e.initialize();

            if (e.getSuccessEffect().isEmpty())
                return 0;

            ForceMoveParam forceMove = e.getForceMoveParam();
            if (forceMove != null) {
                attacked.getMoveController().setEffectTargetX(forceMove.getX());
                attacked.getMoveController().setEffectTargetY(forceMove.getY());
                attacked.getMoveController().setEffectTargetZ(forceMove.getZ());
            }

            ThreadPoolManager.getInstance().schedule(new Runnable() {
                @Override
                public void run() {
                    /*
                     * if (attacked.getEffectController().isAbnormalSet(EffectId.STUMBLE) ||
                     * attacked.getEffectController().isAbnormalSet(EffectId.STAGGER) ||
                     * attacked.getEffectController().isAbnormalSet(EffectId.OPENAERIAL) ||
                     * attacked.getEffectController().isAbnormalSet(EffectId.CANNOT_MOVE)) return;
                     */

                    e.applyEffect();

                    PacketBroadcaster.getInstance().callSingle(e.getEffected());
                }
            }, time);

            return skillId;
        }
    }

    public static void untargetCreature(final Creature creature, boolean forced) {
        for (AionObject obj : creature.getKnownList().getObjects()) {
            if (obj instanceof Creature) {
                Creature cre = (Creature) obj;

                if (cre.getTarget() == creature) {
                    if (cre.isCasting())
                        cre.getController().cancelCurrentSkill(true);

                    if (forced) {
                        cre.setTarget(null);

                        if (cre instanceof Player) {
                            PacketSendUtility.sendPacket(((Player) cre), new SM_TARGET_SELECTED(
                                (Player) cre));
                            PacketSendUtility.broadcastPacket(cre, new SM_TARGET_UPDATE(
                                (Player) cre));
                        }
                        else {
                            PacketSendUtility.broadcastPacket(cre, new SM_LOOKATOBJECT(cre));
                        }
                    }
                }
            }
        }
    }
}
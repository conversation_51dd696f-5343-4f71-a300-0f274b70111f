<?xml version="1.0" encoding="UTF-8"?>
<ae_static_data xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="static_data.xsd">
	<!--
         This file is an entry point for all xml data files.

         Server when starting is reading this file and using XmlMerger is including other
         files according to the <import> tags, output file is saved in /cache/static_data.xml

         XmlMerger creates one big xml file, which is later parsed by XmlDataLoader.
         This way, we can have whole static data within many xml files.

         How to use <import> tags:

             Most basic version is to type:
               <import file="some_file.xml"/>
             Merger will just include content of the some_file.xml (omitting attributes of root node)
             Instead of some_file.xml we can type name of directory, then all xml files in that
             directory ( and deeper - recursively ) will be loaded.


             We can add optional attribute "skipRoot":
               <import file="some_file.xml" skipRoot="true"/>
             This way xml will be included, but without root node.
             It'll be used to split for example npc_data.xml into few files.
             How this would look like:

             We've got ( for example ) 2 xml files with npc_data definitions:

             npc_data_f1.xml:
                 <?xml version="1.0" encoding="UTF-8"?>
                 <npc_data xmlns:xs="http://www.w3.org/2001/XMLSchema-instance"
                         xs:noNamespaceSchemaLocation="static_data.xsd">
                     <npc id="1" name="Npc_N1"/>
                 </npc_data>

             npc_data_f2.xml:
                 <?xml version="1.0" encoding="UTF-8"?>
                 <npc_data xmlns:xs="http://www.w3.org/2001/XMLSchema-instance"
                         xs:noNamespaceSchemaLocation="static_data.xsd">
                     <npc id="2" name="Npc_N2"/>
                 </npc_data>

             In static_data.xml we would have to put:

                 <npc_data>
                     <import file="npc_data_f1.xml" skipRoot="true"/>
                     <import file="npc_data_f2.xml" skipRoot="true"/>
                 </npc_data>

             In the merged file we'll have:
                 <npc_data>
                     <npc id="1" name="Npc_N1"/>
                     <npc id="2" name="Npc_N2"/>
                 </npc_data>

             So, not important things (like schema declarations) were removed and
             root nodes were also removed - because of it we had to put <import> tags
             within <npc_data/> tag.

         static_data.xsd schema file must be valid for use both:
           * by merged xml file ( because it'll be used for validation when loading)
           * by every single xml file ( IDE will use it to validate xml files, when editing)
     -->
	<import file="world_maps.xml"/>
	<import file="npc_trade_list.xml"/>
	<import file="npc_teleporter.xml"/>
	<import file="npc_walker.xml"/>
	<import file="teleport_location.xml"/>
	<import file="player_experience_table.xml"></import>
	<import file="player_initial_data.xml"/>
	<import file="items/item_templates.xml"/>
	<import file="player_titles.xml"/>
	<player_stats_templates>
		<import file="stats/player" skipRoot="true"/>
	</player_stats_templates>
	<summon_stats_templates>
		<import file="stats/summon" skipRoot="true"/>
	</summon_stats_templates>
	<npc_templates>
		<import file="npcs" skipRoot="true"/>
	</npc_templates>
	<skill_data>
		<import file="skills" skipRoot="true"/>
	</skill_data>
	<skill_tree>
		<import file="skill_tree" skipRoot="true"/>
	</skill_tree>
	<bind_points>
		<import file="bind_points" skipRoot="true"/>
	</bind_points>
	<cube_expander>
		<import file="cube_expander" skipRoot="true"/>
	</cube_expander>
	<warehouse_expander>
		<import file="warehouse_expander" skipRoot="true"/>
	</warehouse_expander>
	<gatherable_templates>
		<import file="gatherables" skipRoot="true"/>
	</gatherable_templates>
	<quests>
		<import file="quest_data" skipRoot="true"/>
	</quests>
	<quest_scripts>
		<import file="quest_script_data" skipRoot="true"/>
	</quest_scripts>
	<zones>
		<import file="zones" skipRoot="true"/>
	</zones>
	<flight_zones>
		<import file="flight_zones" skipRoot="true"/>
	</flight_zones>
	<goodslists>
		<import file="goodslists" skipRoot="true"/>
	</goodslists>
	<spawns>
		<import file="spawns" skipRoot="true"/>
	</spawns>
	<tribe_relations>
		<import file="tribe" skipRoot="true"/>
	</tribe_relations>
	<recipe_templates>
		<import file="recipe" skipRoot="true"/>
	</recipe_templates>
	<portal_templates>
		<import file="portals" skipRoot="true"/>
	</portal_templates>
	<item_sets>
		<import file="item_sets" skipRoot="true"/>
	</item_sets>
	<npc_skill_templates>
		<import file="npc_skills" skipRoot="true"/>
	</npc_skill_templates>
	<pet_skill_templates>
		<import file="pet_skills" skipRoot="true"/>
	</pet_skill_templates>
	<siege_locations>
		<import file="siege/siege_locations.xml" skipRoot="true"/>
	</siege_locations>
	<siege_spawns>
		<import file="siege/siege_spawns.xml" skipRoot="true"/>
	</siege_spawns>
	<shields>
		<import file="shields/shields.xml" skipRoot="true"/>
	</shields>
	<bonuses>
		<import file="bonuses/bonuses.xml" skipRoot="true"/>
	</bonuses>
	<fly_rings>
		<import file="fly_rings/fly_rings.xml" skipRoot="true"/>
	</fly_rings>
	<pets>
		<import file="pets/pets.xml" skipRoot="true"/>
	</pets>
	<compressed_items>
		<import file="compressed_item" skipRoot="true"/>
	</compressed_items>
	<pvpevent_data>
		<bosses>
			<import file="pvpevent_data/pvpevent_bosses.xml" skipRoot="true" />
		</bosses>
		<helpers>
			<import file="pvpevent_data/pvpevent_helpers.xml" skipRoot="true" />
		</helpers>
		<spawnpoints>
			<import file="pvpevent_data/pvpevent_spawnpoints.xml" skipRoot="true" />
		</spawnpoints>
	</pvpevent_data>
	<wrapped_items>
		<import file="items/wrapped_items.xml" skipRoot="true"/>
	</wrapped_items>
	<mount_data>
		<import file="mount/mount_data.xml" skipRoot="true"/>
	</mount_data>
	<charge_skills>
		<import file="charge_skills/charge_skills.xml" skipRoot="true"/>
	</charge_skills>
	<safezones>
		<import file="safezones" skipRoot="true"/>
	</safezones>
	<animations_client>
		<import file="animations/animations_client.xml" skipRoot="true"/>
	</animations_client>
	<animations_full>
		<import file="animations/animations_full.xml" skipRoot="true"/>
	</animations_full>
	<polishes>
		<import file="polish/polish_list.xml" skipRoot="true"/>
	</polishes>
	<random_options>
		<import file="random_option/random_option_list.xml" skipRoot="true"/>
	</random_options>
	<staticdoors>
		<import file="staticdoors/staticdoors.xml" skipRoot="true"/>
	</staticdoors>
</ae_static_data>

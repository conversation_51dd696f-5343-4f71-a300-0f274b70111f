<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="teleport_location">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="teleloc_template" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence/>
						<xs:attribute name="loc_id" type="xs:int" use="required"/>
						<xs:attribute name="mapid" type="xs:int" use="required"/>
						<xs:attribute name="name" type="xs:string" use="required"/>
						<xs:attribute name="teleportid" type="xs:int"/>
						<xs:attribute name="posX" type="xs:float"/>
						<xs:attribute name="posY" type="xs:float"/>
						<xs:attribute name="posZ" type="xs:float"/>
						<xs:attribute name="heading" type="xs:int"/>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>

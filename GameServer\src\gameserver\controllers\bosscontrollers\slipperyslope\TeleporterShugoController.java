/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers.slipperyslope;

import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.ChatType;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.RequestResponseHandler;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_QUESTION_WINDOW;
import gameserver.services.EventService;
import gameserver.services.TeleportService;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

/**
 * <AUTHOR>
 * 
 */
public class TeleporterShugoController extends BossController {
    public TeleporterShugoController() {
        super(205516, true);
    }

    protected void think() {
        // nothing
    }

    @Override
    public void onCreation() {
        getOwner().setCustomTag("Jotun Stronghold");
    }

    @Override
    public void onDialogRequest(Player player) {
        if (!EventService.getInstance().areInstancesEnabled()) {
            message(player, "The Jotun Stronghold is currently closed.");
            return;
        }

        message(player, "I can teleport you to the Staging Area of the instance.");
        request(player);
    }

    private void request(Player player) {
        RequestResponseHandler responseHandler = new RequestResponseHandler(getOwner()) {
            @Override
            public void acceptRequest(Creature requester, final Player responder) {
                TeleportService.teleportTo(responder, 140010000, 219.52f, 266.19f, 97.25f,
                    TeleportService.TELEPORT_PORTAL_DELAY);

                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        responder.setAllFriend(true);
                    }
                }, TeleportService.TELEPORT_PORTAL_DELAY + 500);
            }

            @Override
            public void denyRequest(Creature requester, Player responder) {
            }
        };

        boolean requested = player.getResponseRequester().putRequest(SM_QUESTION_WINDOW.STR_CUSTOM,
            responseHandler);
        if (requested) {
            PacketSendUtility.sendPacket(player, new SM_QUESTION_WINDOW(
                SM_QUESTION_WINDOW.STR_CUSTOM, 0,
                "Do you wish to teleport to the Jotun Stronghold staging area?", ""));
        }
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendPacket(player, new SM_MESSAGE(getOwner().getObjectId(),
            "Assistant Recordkeeper", msg, ChatType.ALLIANCE));
    }
}
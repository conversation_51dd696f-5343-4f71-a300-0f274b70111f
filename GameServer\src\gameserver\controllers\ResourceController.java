/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers;

import gameserver.controllers.movement.ActionObserver;
import gameserver.controllers.movement.ActionObserver.ObserverType;
import gameserver.controllers.movement.StartMovingListener;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.pvpevents.Battleground;
import gameserver.model.templates.gather.Material;
import gameserver.network.aion.serverpackets.SM_DELETE;
import gameserver.skillengine.task.ResourceGatherTask;
import gameserver.utils.PacketSendUtility;
import gameserver.world.World;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class ResourceController extends GatherableController {
    private Battleground bg;
    private ResourceGatherTask task;
    private ActionObserver attackedObserver = null;

    public ResourceController(Battleground bg) {
        this.bg = bg;
    }

    @Override
    public void onStartUse(final Player player) {
        if (state != GatherState.GATHERING) {
            state = GatherState.GATHERING;
            currentGatherer = player.getObjectId();

            player.getObserveController().attach(new StartMovingListener() {
                @Override
                public void moved() {
                    finishGathering(player);
                }
            });

            player.getObserveController().attach(new ActionObserver(ObserverType.DEATH) {
                @Override
                public void died(Creature creature) {
                    finishGathering(player);
                }
            });

            attackedObserver = new ActionObserver(ObserverType.ATTACKED) {
                private AtomicInteger counter = new AtomicInteger(0);

                @Override
                public void attacked(Creature creature) {
                    if (counter.incrementAndGet() >= 8) {
                        finishGathering(player);
                        player.getObserveController().removeObserver(this);
                    }
                }
            };

            player.getObserveController().addObserver(attackedObserver);

            task = new ResourceGatherTask(player, getOwner(), new Material("Iron Ore", 152000201,
                1404019, 10000000));
            task.start();
        }
    }

    @Override
    public void completeInteraction(Player player) {
        state = GatherState.IDLE;
        World.getInstance().despawn(getOwner());
        getOwner().getController().delete();
        PacketSendUtility.broadcastPacketAndReceive(player, new SM_DELETE(getOwner(), 1));
    }

    @Override
    public void rewardPlayer(Player player) {
        if (player == null)
            return;

        if (player.getBattleground() == null || bg == null)
            return;

        if (player.isInAlliance())
            bg.onResourceGathered(getOwner(), player.getPlayerAlliance().getBgIndex());
        else if (player.isInGroup())
            bg.onResourceGathered(getOwner(), player.getPlayerGroup().getBgIndex());
        else
            bg.onResourceGathered(getOwner(), player.getBgIndex());
    }

    public void finishGathering(Player player) {
        if (currentGatherer == player.getObjectId()) {
            if (state == GatherState.GATHERING) {
                task.abort();

                if (attackedObserver != null)
                    player.getObserveController().removeObserver(attackedObserver);
            }

            currentGatherer = 0;
            state = GatherState.IDLE;
        }
    }
}
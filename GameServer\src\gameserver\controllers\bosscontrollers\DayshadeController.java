/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.Creature;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class DayshadeController extends BossController {
    private final BossSkill AETHERIC_TURBULENCE = new BossSkill(19156, 10);
    private final BossSkill DIVINE_TOUCH = new BossSkill(19170, 1);
    private final BossSkill UNSTABLE_EARTH = new BossSkill(19176, 1);
    private final BossSkill POWERFUL_BRANDISH = new BossSkill(18611, 1);

    private final int FLASH_LAPILIMO = 281896;
    private final int NOBLE_LAPILIMA = 216946;

    private Mode mode = Mode.WAITING;

    private int addWavesDone = 0;
    private int resetTicks = 0;

    private long nextMove = 0;

    public DayshadeController() {
        super(282010, true);
    }

    @Override
    protected void think() {
        Creature owner = getOwner();

        if (owner.getAggroList().getMostHated() == null
            || !owner.getKnownList().knows(owner.getAggroList().getMostHated()))
            resetTicks++;

        if (resetTicks >= 60) {
            addWavesDone = 0;
            resetTicks = 0;
            mode = Mode.WAITING;
        }

        int hp = owner.getLifeStats().getHpPercentage();
        if (addWavesDone < 1 && hp <= 75) {
            spawnAdds(NOBLE_LAPILIMA, 3);
            spawnAdds(FLASH_LAPILIMO, 3);
            addWavesDone++;
        }
        else if (addWavesDone < 2 && hp <= 50) {
            spawnAdds(FLASH_LAPILIMO, 5);
            addWavesDone++;
        }
        else if (addWavesDone < 3 && hp <= 25) {
            spawnAdds(FLASH_LAPILIMO, 10);
            spawnAdds(NOBLE_LAPILIMA, 1);
            addWavesDone++;
        }

        switch (mode) {
            case WAITING:
                if (owner.getAggroList().getMostHated() != null) {
                    mode = Mode.ACTIVE;
                }
                else {
                    if (nextMove == 0) {
                        nextMove = System.currentTimeMillis() + Rnd.get(10000, 20000);
                    }
                    else if (System.currentTimeMillis() > nextMove) {
                        nextMove = 0;

                        randomWalk(10);
                    }
                }
                break;

            case ACTIVE:
                if (owner.getLifeStats().getHpPercentage() < 50) {
                    mode = Mode.ENRAGED;
                    break;
                }

                if (POWERFUL_BRANDISH.timeSinceUse() > 25)
                    queueSkill(POWERFUL_BRANDISH, getRandomTarget());
                else if (DIVINE_TOUCH.timeSinceUse() > 20)
                    queueSkill(DIVINE_TOUCH, getPriorityTarget());
                else if (AETHERIC_TURBULENCE.timeSinceUse() > 12)
                    queueSkill(AETHERIC_TURBULENCE, getMostHated());
                break;
            case ENRAGED:
                getOwner().getAggroList().addHate(getPriorityTarget(), 5000);

                if (UNSTABLE_EARTH.timeSinceUse() > 30)
                    queueSkill(UNSTABLE_EARTH, getPriorityTarget());
                else if (POWERFUL_BRANDISH.timeSinceUse() > 15)
                    queueSkill(POWERFUL_BRANDISH, getRandomTarget());
                else if (DIVINE_TOUCH.timeSinceUse() > 8)
                    queueSkill(DIVINE_TOUCH, getPriorityTarget());
                else if (AETHERIC_TURBULENCE.timeSinceUse() > 6)
                    queueSkill(AETHERIC_TURBULENCE, getMostHated());
                break;
        }
    }

    enum Mode {
        WAITING,
        ACTIVE,
        ENRAGED
    }
}
/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.DropService;

/**
 * <AUTHOR>
 * 
 */
public class TombstoneController extends BossController {
    public TombstoneController() {
        super(700047);
        
        if (thinkTask != null) {
            thinkTask.cancel(true);
            thinkTask = null;
        }
    }
    
    protected void think() {
        // Do nothing
    }
    
    @Override
    public void onDialogRequest(Player player) {
        if (player.getBattleground() == null || player.isSpectating())
            return;
        
        DropService.getInstance().requestDropList(player, getOwner().getObjectId());
    }
}

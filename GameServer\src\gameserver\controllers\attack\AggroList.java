/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.attack;

import gameserver.ai.events.Event;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.services.DuelService;
import gameserver.utils.MathUtil;
import gameserver.utils.stats.StatFunctions;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.log4j.Logger;

/**
 * <AUTHOR> KKnD
 */
public class AggroList {
    @SuppressWarnings("unused")
    private static final Logger log = Logger.getLogger(AggroList.class);

    private Creature owner;

    private Map<Creature, AggroInfo> aggroList = new ConcurrentHashMap<Creature, AggroInfo>();

    public AggroList(Creature owner) {
        this.owner = owner;
    }

    public void setOwner(Creature owner) {
        this.owner = owner;
    }

    /**
     * Only add damage from enemies. (Verify this includes summons, traps, pets, and excludes fall damage.)
     * 
     * @param creature
     * @param damage
     */
    public void addDamage(Creature creature, int damage) {
        if (creature == null || !owner.isEnemy(creature))
            return;

        AggroInfo ai = getAggroInfo(creature);
        ai.addDamage(damage);
        /**
         * For now we add hate equal to each damage received Additionally there will be broadcast of extra hate
         */
        ai.addHate(creature instanceof Player ? StatFunctions.calculateHate(creature, damage)
            : damage);

        if (creature instanceof Player)
            creature.getController().broadcastHate(
                StatFunctions.calculateHate(creature, damage) / 3);

        owner.getAi().handleEvent(Event.ATTACKED);
    }

    /**
     * Extra hate that is received from using non-damange skill effects
     * 
     * @param creature
     * @param hate
     */
    public void addHate(Creature creature, int hate) {
        if (creature == null || creature == owner || !owner.isEnemy(creature))
            return;

        AggroInfo ai = getAggroInfo(creature);
        ai.addHate(hate);

        owner.getAi().handleEvent(Event.ATTACKED);
    }

    /**
     * @return player/group/alliance with most damage.
     */
    public AionObject getMostDamage() {
        AionObject mostDamage = null;
        int maxDamage = 0;

        for (AggroInfo ai : getFinalDamageList(true)) {
            if (ai.getAttacker() == null)
                continue;

            if (ai.getDamage() > maxDamage) {
                mostDamage = ai.getAttacker();
                maxDamage = ai.getDamage();
            }
        }

        return mostDamage;
    }

    /**
     * @return player with most damage
     */
    public Player getMostPlayerDamage() {
        if (aggroList.isEmpty())
            return null;

        Player mostDamage = null;
        int maxDamage = 0;

        // Use final damage list to get pet damage as well.
        for (AggroInfo ai : this.getFinalDamageList(false)) {
            if (((Player) ai.getAttacker()).getLifeStats() == null)
                continue;
            else if (!((Player) ai.getAttacker()).isEnemy(owner))
                continue;

            if (ai.getDamage() > maxDamage) {
                mostDamage = (Player) ai.getAttacker();
                maxDamage = ai.getDamage();
            }
        }

        return mostDamage;
    }

    /**
     * @return most hated creature
     */
    public Creature getMostHated() {
        if (aggroList.isEmpty())
            return null;

        Creature mostHated = null;
        int maxHate = 0;

        for (AggroInfo ai : aggroList.values()) {
            if (ai == null)
                continue;

            // aggroList will never contain anything but creatures
            Creature attacker = (Creature) ai.getAttacker();

            if (attacker == null || attacker.getLifeStats() == null
                || attacker.getLifeStats().isAlreadyDead() || !owner.getKnownList().knows(attacker)
                || (System.currentTimeMillis() - ai.getTime()) > 60 * 1000
                || !owner.isEnemy(attacker))
                ai.setHate(0);

            if (ai.getHate() > maxHate) {
                mostHated = attacker;
                maxHate = ai.getHate();
            }
        }

        return mostHated;
    }

    /**
     * @param creature
     *            the creature to check
     * @return boolean
     */
    public boolean isMostHated(Creature creature) {
        if (creature == null || creature.getLifeStats().isAlreadyDead())
            return false;

        Creature mostHated = getMostHated();
        if (mostHated == null)
            return false;

        return mostHated.equals(creature);
    }

    /**
     * @param creature
     * @param value
     */
    public void notifyHate(Creature creature, int value) {
        if (!MathUtil.isIn3dRange(creature, owner, 20))
            return;

        if (isHating(creature))
            addHate(creature, value);
        else if (owner instanceof Player)
            addHate(creature, value);
    }

    /**
     * @param creature
     */
    public void stopHating(Creature creature) {
        AggroInfo aggroInfo = aggroList.get(creature);
        if (aggroInfo != null) {
            aggroInfo.setHate(0);

            if (owner.getAi().isLogging())
                log.info("AGGRO objectId " + owner.getObjectId() + " stopHating("
                    + creature.getName() + ")");
        }
    }

    /**
     * Remove completely creature from aggro list
     * 
     * @param creature
     */
    public void remove(Creature creature) {
        aggroList.remove(creature);
    }

    /**
     * Clear aggroList
     */
    public void clear() {
        aggroList.clear();
    }

    /**
     * Clear hate from aggroList
     */
    public void clearHate() {
        for (AggroInfo ai : aggroList.values()) {
            ai.setHate(0);
        }

        if (owner.getAi().isLogging())
            log.info("AGGRO objectId " + owner.getObjectId() + " clearHate()");
    }

    /**
     * @param creature
     * @return aggroInfo
     */
    public AggroInfo getAggroInfo(Creature creature) {
        AggroInfo ai = aggroList.get(creature);
        if (ai == null) {
            ai = new AggroInfo(creature);
            aggroList.put(creature, ai);
        }
        return ai;
    }

    /**
     * @param creature
     * @return boolean
     */
    private boolean isHating(Creature creature) {
        return aggroList.containsKey(creature);
    }

    /**
     * @return aggro list
     */
    public Collection<AggroInfo> getList() {
        return aggroList.values();
    }

    /**
     * @return total damage
     */
    public int getTotalDamage() {
        int totalDamage = 0;
        for (AggroInfo ai : this.aggroList.values()) {
            totalDamage += ai.getDamage();
        }
        return totalDamage;
    }

    /**
     * @return total hate
     */
    public int getTotalHate() {
        int totalHate = 0;
        for (AggroInfo ai : this.aggroList.values()) {
            totalHate += ai.getHate();
        }
        return totalHate;
    }

    /**
     * Used to get a list of AggroInfo with player/group/alliance damages combined. - Includes only AggroInfo with
     * PlayerAlliance, PlayerGroup, and Player objects.
     * 
     * @return finalDamageList including players/groups/alliances
     */
    public Collection<AggroInfo> getFinalDamageList(boolean mergeGroupDamage) {
        final Map<AionObject, AggroInfo> list = new HashMap<AionObject, AggroInfo>();

        for (AggroInfo ai : this.aggroList.values()) {
            if (!(ai.getAttacker() instanceof Creature))
                continue;

            // Check to see if this is a summon, if so add the damage to the group.

            Creature master = ((Creature) ai.getAttacker()).getMaster();

            if (!(master instanceof Player))
                continue;

            // Don't include non-enemies
            if (!master.isEnemy(owner))
                continue;

            Player player = (Player) master;

            // Don't include damage from players outside the known list.
            if (!owner.getKnownList().knows(player))
                continue;

            if (mergeGroupDamage) {
                AionObject source;

                if (player.isInAlliance()) {
                    source = player.getPlayerAlliance();
                }
                else if (player.isInGroup()) {
                    source = player.getPlayerGroup();
                }
                else {
                    source = player;
                }

                if (list.containsKey(source)) {
                    (list.get(source)).addDamage(ai.getDamage());
                    (list.get(source)).addHate(ai.getHate());
                    (list.get(source)).setTime(ai.getTime());
                }
                else {
                    AggroInfo aggro = new AggroInfo(source);
                    aggro.setDamage(ai.getDamage());
                    aggro.setHate(ai.getHate());
                    aggro.setTime(ai.getTime());
                    list.put(source, aggro);
                }
            }
            else if (list.containsKey(player)) {
                // Summon or other assistance
                list.get(player).addDamage(ai.getDamage());
                list.get(player).addHate(ai.getHate());
                list.get(player).setTime(ai.getTime());
            }
            else {
                // Create a separate object so we don't taint current list.
                AggroInfo aggro = new AggroInfo(player);
                aggro.addDamage(ai.getDamage());
                aggro.addHate(ai.getHate());
                aggro.setTime(ai.getTime());
                list.put(player, aggro);
            }
        }

        return list.values();
    }

    /**
     * @return the winner
     */
    public Player getWinner(Creature lastAttacker) {
        if (lastAttacker != null && lastAttacker.getObjectId() == owner.getObjectId())
            lastAttacker = null;

        if (lastAttacker != null && lastAttacker.getMaster() != null)
            lastAttacker = lastAttacker.getMaster();

        Player mostDamage = getMostPlayerDamage();

        if (mostDamage != null
            && DuelService.getInstance().isDueling(owner.getObjectId(), mostDamage.getObjectId())
            && lastAttacker instanceof Player)
            return (Player) lastAttacker;

        if (lastAttacker != null && lastAttacker instanceof Player
            && aggroList.get(lastAttacker) != null) {
            if (aggroList.get(lastAttacker).getDamage()
                / ((float) lastAttacker.getGameStats().getCurrentStat(StatEnum.MAXHP)) >= 0.25F)
                return (Player) lastAttacker;
        }

        return mostDamage;
    }
}

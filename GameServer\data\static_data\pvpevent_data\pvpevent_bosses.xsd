<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	version="1.0">
	<xs:include schemaLocation="../import.xsd" />
	<xs:element name="bosses" type="bossgroup" />
	<xs:complexType name="bossgroup">
		<xs:sequence>
			<xs:element ref="import" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="boss" type="boss" minOccurs="0"
				maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="boss">
		<xs:sequence>
			<xs:element name="npc_id" type="xs:nonNegativeInteger"
				minOccurs="1" maxOccurs="1" />
		</xs:sequence>
		<xs:attribute name="scale_factor" use="optional" default="1">
			<xs:simpleType>
				<xs:restriction base="xs:int">
					<xs:minExclusive value="0" />
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
</xs:schema>
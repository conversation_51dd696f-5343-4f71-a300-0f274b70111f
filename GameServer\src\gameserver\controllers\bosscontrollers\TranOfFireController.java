/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.ai.events.Event;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.state.CreatureVisualState;

/**
 * <AUTHOR>
 */
public class TranOfFireController extends BossController {
    private BossSkill ONE_SHOT = new BossSkill(17076, 1);

    public TranOfFireController() {
        super(217527, true);
    }

    @Override
    protected void think() {
        Creature owner = getOwner();

        if (!owner.isInVisualState(CreatureVisualState.HIDE2))
            owner.setVisualState(CreatureVisualState.HIDE2);

        if (Math.abs(owner.getZ() - owner.getSpawn().getZ()) > 5) {
            owner.getAggroList().clearHate();
            owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);
            return;
        }
        else if (owner.getAggroList().getMostHated() != null
            && Math.abs(owner.getAggroList().getMostHated().getZ() - owner.getZ()) > 5) {
            owner.getAggroList().clearHate();
            owner.getAi().handleEvent(Event.TIRED_ATTACKING_TARGET);
            return;
        }

        if (owner.getTarget() instanceof Creature
            && !((Creature) owner.getTarget()).getLifeStats().isAlreadyDead())
            queueSkill(ONE_SHOT, getMostHated());
    }
}
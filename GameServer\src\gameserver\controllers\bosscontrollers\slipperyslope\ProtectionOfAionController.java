/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers.slipperyslope;

import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.MathUtil;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class ProtectionOfAionController extends BossController {
    private final BossSkill STUN = new BossSkill(17988, 1);
    private final BossSkill MAGIC = new BossSkill(18224, 1);
    
    public ProtectionOfAionController() {
        super(282469);
    }

    protected void think() {
        Npc owner = getOwner();
        
        Player priority = getPriorityTarget();
        if (priority == null || !MathUtil.isIn3dRange(owner, priority, 35))
            return;

        getOwner().getAggroList().addHate(priority, 10000);

        if (STUN.timeSinceUse() > 30 && Rnd.get(100) < 15)
            queueSkill(STUN, owner);
        else if (MAGIC.timeSinceUse() > 20 && Rnd.get(100) < 15)
            queueSkill(MAGIC, owner);
    }
}

/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.effect;

import gameserver.model.PlayerClass;
import gameserver.model.alliance.PlayerAllianceEvent;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Summon;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.group.GroupEvent;
import gameserver.network.aion.serverpackets.SM_ABNORMAL_EFFECT;
import gameserver.network.aion.serverpackets.SM_ABNORMAL_STATE;
import gameserver.services.AllianceService;
import gameserver.skillengine.effect.EffectId;
import gameserver.skillengine.effect.EffectTemplate;
import gameserver.skillengine.effect.TransformEffect;
import gameserver.skillengine.model.ActivationAttribute;
import gameserver.skillengine.model.DispelCategoryType;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.SkillSubType;
import gameserver.skillengine.model.SkillTargetSlot;
import gameserver.skillengine.model.SkillType;
import gameserver.taskmanager.tasks.PacketBroadcaster.BroadcastMode;
import gameserver.utils.PacketSendUtility;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class EffectController {
    private Creature owner;

    protected Map<String, Effect> passiveEffectMap = new ConcurrentHashMap<String, Effect>();
    protected Map<String, Effect> noshowEffects = new ConcurrentHashMap<String, Effect>();
    protected Map<String, Effect> abnormalEffectMap = new ConcurrentHashMap<String, Effect>();
    protected Map<Long, Effect> rangerBuffEffectMap = new TreeMap<Long, Effect>();

    protected int abnormals;

    private long nextStumble = 0;
    private long nextStun = 0;
    private long nextStagger = 0;

    public EffectController(Creature owner) {
        this.owner = owner;
    }

    /**
     * @return the owner
     */
    public Creature getOwner() {
        return owner;
    }

    public void setOwner(Creature owner) {
        this.owner = owner;
    }

    public boolean hasAbnormalEffect(int skillId) {
        for (Effect e : abnormalEffectMap.values()) {
            if (e.getSkillId() == skillId)
                return true;
        }
        return false;
    }

    public boolean hasAbnormalEffect(String stack) {
        for (Effect e : abnormalEffectMap.values()) {
            if (e.getStack().equals(stack))
                return true;
        }

        return false;
    }

    /**
     * @param effect
     */
    public void addEffect(Effect effect) {
        Map<String, Effect> mapToUpdate = getMapForEffect(effect);

        Effect existingEffect = mapToUpdate.get(effect.getStack());
        if (existingEffect != null) {
            // check stack level
            if (existingEffect.getSkillStackLvl() > effect.getSkillStackLvl())
                return;
            // check skill level (when stack level same)
            if (existingEffect.getSkillStackLvl() == effect.getSkillStackLvl()
                && existingEffect.getSkillLevel() > effect.getSkillLevel())
                return;

            existingEffect.endEffect();
        }

        for (Effect ef : mapToUpdate.values()) {
            if (ef.getTargetSlot() != effect.getTargetSlot())
                continue;

            boolean end = false;

            for (EffectTemplate eft : effect.getEffectTemplates()) {
                if (eft.getEffectId() == 0 || eft instanceof TransformEffect)
                    continue;

                EffectTemplate et = ef.getTemplateWithEffectId(eft.getEffectId());

                if (et != null) {
                    if (et.getBasicLvl() <= eft.getBasicLvl()) {
                        end = true;
                        break;
                    }
                    else {
                        effect.setForbidAdding(true);
                        effect.setAddedToController(false);
                        return;
                    }
                }
            }

            if (end) {
                ef.endEffect();
            }
        }

        if (effect.isToggle()) {
            switch (effect.getSkillSubType()) {
                case CHANT:
                    Collection<Effect> auras = getAuras();
                    if (auras.size() >= 3) {
                        Iterator<Effect> iter = auras.iterator();
                        Effect nextEffect = iter.next();
                        nextEffect.endEffect();
                        noshowEffects.remove(nextEffect.getStack());
                    }
                    break;
                default:
                    if (effect.getEffectsDuration() > 0)
                        break;

                    Collection<Effect> toggles = getTogglesWithoutAuras();
                    if (toggles.size() >= (owner instanceof Player
                        && (((Player) owner).isRidingRobot() || ((Player) owner).getPlayerClass() == PlayerClass.RANGER) ? 2
                            : 1)) {
                        Iterator<Effect> iter = toggles.iterator();
                        Effect nextEffect = iter.next();
                        nextEffect.endEffect();
                        noshowEffects.remove(nextEffect.getStack());
                    }
                    break;
            }
        }

        if (effect.isLimitedRangerBuff()) {
            List<Long> entriesToRemove = new ArrayList<Long>(rangerBuffEffectMap.size());

            for (Entry<Long, Effect> entry : rangerBuffEffectMap.entrySet()) {
                if (entry.getValue() == null || entry.getValue().isStopped())
                    entriesToRemove.add(entry.getKey());
            }

            for (Long entry : entriesToRemove)
                rangerBuffEffectMap.remove(entry);

            if (rangerBuffEffectMap.size() >= 2) {
                Iterator<Effect> iter = rangerBuffEffectMap.values().iterator();
                Effect nextEffect = iter.next();
                nextEffect.endEffect();
                iter.remove();
            }

            rangerBuffEffectMap.put(System.currentTimeMillis(), effect);
        }

        mapToUpdate.put(effect.getStack(), effect);
        effect.startEffect(false);

        if (!effect.isPassive()) {
            broadCastEffects();
        }
    }

    private Collection<Effect> getAuras() {
        Collection<Effect> auras = new ArrayList<Effect>(noshowEffects.size());

        for (Iterator<Effect> it = noshowEffects.values().iterator(); it.hasNext();) {
            Effect effect = it.next();
            if (effect.getSkillSubType() == SkillSubType.CHANT)
                auras.add(effect);
        }

        return auras;
    }

    private Collection<Effect> getTogglesWithoutAuras() {
        Collection<Effect> toggles = new ArrayList<Effect>(noshowEffects.size());

        for (Iterator<Effect> it = noshowEffects.values().iterator(); it.hasNext();) {
            Effect effect = it.next();
            if (effect.getSkillSubType() != SkillSubType.CHANT && effect.getEffectsDuration() == 0)
                toggles.add(effect);
        }

        return toggles;
    }

    /**
     * @param effect
     * @return
     */
    private Map<String, Effect> getMapForEffect(Effect effect) {
        if (effect.isPassive())
            return passiveEffectMap;

        if (effect.isToggle())
            return noshowEffects;

        return abnormalEffectMap;
    }

    /**
     * @param stack
     * @return abnormalEffectMap
     */
    public Effect getAnormalEffect(String stack) {
        return abnormalEffectMap.get(stack);
    }

    public void broadCastEffects() {
        owner.addPacketBroadcastMask(BroadcastMode.BROAD_CAST_EFFECTS);
    }

    /**
     * Broadcasts current effects to all visible objects
     */
    public void broadCastEffectsImp() {
        Collection<Effect> effects = getAbnormalEffects();
        PacketSendUtility.broadcastPacket(getOwner(), new SM_ABNORMAL_EFFECT(getOwner(), abnormals,
            effects));
    }

    /**
     * Used when player see new player
     * 
     * @param player
     */
    public void sendEffectIconsTo(Player player) {
        Collection<Effect> effects = getAbnormalEffects();
        PacketSendUtility
            .sendPacket(player, new SM_ABNORMAL_EFFECT(getOwner(), abnormals, effects));
    }

    /**
     * @param effect
     */
    public void clearEffect(Effect effect) {
        abnormalEffectMap.remove(effect.getStack());
        noshowEffects.remove(effect.getStack());
        passiveEffectMap.remove(effect.getStack());

        if (!effect.isPassive()) {
            broadCastEffects();
        }
    }

    /**
     * Removes the effect by skillid.
     * 
     * @param skillid
     */
    public void removeEffect(int skillid) {
        for (Effect effect : abnormalEffectMap.values()) {
            if (effect.getSkillId() == skillid) {
                effect.endEffect();
                abnormalEffectMap.remove(effect.getStack());
            }
        }
    }

    public void removeSummonEffects(Summon summon) {
        for (Effect effect : abnormalEffectMap.values()) {
            if (effect.getEffector() == summon) {
                if (effect.getStack().contains("NPC_SKILLN_ORDER_ELEMENTALFIELD_")) // Spirit Wall of Protection
                    continue;

                effect.endEffect();
                abnormalEffectMap.remove(effect.getStack());
            }
        }
    }

    /**
     * Removes the effect by skillids.
     * 
     * @param skillids
     */
    public void removeEffects(List<Integer> skillIds) {
        for (Effect effect : abnormalEffectMap.values()) {
            for (int skillId : skillIds) {
                if (effect.getSkillId() == skillId) {
                    effect.endEffect();
                    abnormalEffectMap.remove(effect.getStack());
                }
            }
        }
    }

    /**
     * Removes the effect by SkillSetException Number.
     * 
     * @param SkillSetException
     *            Number
     */
    public void removeEffectBySetNumber(final int setNumber) {
        for (Effect effect : abnormalEffectMap.values()) {
            if (effect.getSkillSetException() == setNumber) {
                effect.endEffect();
                abnormalEffectMap.remove(effect.getStack());
            }
        }
        for (Effect effect : passiveEffectMap.values()) {
            if (effect.getSkillSetException() == setNumber) {
                effect.endEffect();
                passiveEffectMap.remove(effect.getStack());
            }
        }
        for (Effect effect : noshowEffects.values()) {
            if (effect.getSkillSetException() == setNumber) {
                effect.endEffect();
                noshowEffects.remove(effect.getStack());
            }
        }
    }

    /**
     * @param effectId
     */
    public void removeEffectByEffectId(int effectId) {
        for (Effect effect : abnormalEffectMap.values()) {
            if (effect.containsEffectId(effectId)) {
                effect.endEffect();
                abnormalEffectMap.remove(effect.getStack());
            }
        }
    }

    /**
     * @param targetSlot
     * @param count
     */
    public void removeEffectByTargetSlot(SkillTargetSlot targetSlot, int count) {
        for (Effect effect : abnormalEffectMap.values()) {
            if (count == 0)
                break;

            if (effect.getTargetSlot() == targetSlot.ordinal()) {
                if (targetSlot == SkillTargetSlot.BUFF && effect.getTargetSlotLevel() > 1)
                    continue;
                effect.endEffect();
                abnormalEffectMap.remove(effect.getStack());
                count--;
            }
        }
    }

    /**
     * @param targetSlot
     * @param count
     */
    public boolean isRemovedEffectByTargetSlot(SkillTargetSlot targetSlot, int count) {
        for (Effect effect : abnormalEffectMap.values()) {
            if (count == 0)
                break;

            if (effect.getTargetSlot() == targetSlot.ordinal()) {
                if (targetSlot == SkillTargetSlot.BUFF && effect.getTargetSlotLevel() > 1)
                    continue;
                effect.endEffect();
                abnormalEffectMap.remove(effect.getStack());
                count--;
            }

            if (count == 0)
                return true;
        }

        return false;
    }

    /**
     * @param targetSlot
     */
    public void removeEffectByTargetSlot(SkillTargetSlot targetSlot) {
        for (Effect effect : abnormalEffectMap.values()) {
            if (effect.getTargetSlot() == targetSlot.ordinal()) {
                if (targetSlot == SkillTargetSlot.BUFF && effect.getTargetSlotLevel() > 1)
                    continue;
                effect.endEffect();
                abnormalEffectMap.remove(effect.getStack());
            }
        }
    }

    /**
     * @param skillType
     * @param value
     */
    public void removeEffectBySkillType(SkillType skillType, int value) {
        for (Effect effect : abnormalEffectMap.values()) {
            if (value == 0)
                break;

            if (effect.getSkillType() == skillType) {
                effect.endEffect();
                abnormalEffectMap.remove(effect.getStack());
                value--;
            }
        }
    }

    public void removeRobotEffects() {
        for (Effect ef : getAbnormalEffects())
            if (ef.getSkillTemplate().getRequiredWeapon() != null
                && ef.getSkillTemplate().getRequiredWeapon().equals("0000000000000100"))
                ef.endEffect();
    }

    /**
     * Removes the effect by skillid.
     * 
     * @param skillid
     */
    public void removePassiveEffect(int skillid) {
        for (Effect effect : passiveEffectMap.values()) {
            if (effect.getSkillId() == skillid) {
                effect.endEffect();
                passiveEffectMap.remove(effect.getStack());
            }
        }
    }

    /**
     * @param skillid
     */
    public void removeNoshowEffect(int skillid) {
        for (Effect effect : noshowEffects.values()) {
            if (effect.getSkillId() == skillid) {
                effect.endEffect();
                noshowEffects.remove(effect.getStack());
            }
        }
    }

    /**
     * @param targetSlot
     * @see TargetSlot
     */
    public void removeAbnormalEffectsByTargetSlot(SkillTargetSlot targetSlot) {
        for (Effect effect : abnormalEffectMap.values()) {
            if (effect.getTargetSlot() == targetSlot.ordinal()) {
                effect.endEffect();
                abnormalEffectMap.remove(effect.getStack());
            }
        }
    }

    /**
     * Removes all effects from controllers and ends them appropriately Passive effect will not be removed
     */
    public void removeAllEffects(boolean clearAll) {
        for (Effect effect : abnormalEffectMap.values()) {
            effect.endEffect();
        }
        abnormalEffectMap.clear();
        for (Effect effect : noshowEffects.values()) {
            effect.endEffect();
        }
        noshowEffects.clear();

        if (clearAll) {
            for (Effect effect : passiveEffectMap.values()) {
                effect.endEffect();
            }
            passiveEffectMap.clear();
            for (Effect effect : rangerBuffEffectMap.values()) {
                effect.endEffect();
            }
            rangerBuffEffectMap.clear();
        }
    }

    public void removeAllNonItemEffects() {
        for (Map.Entry<String, Effect> entry : abnormalEffectMap.entrySet()) {
            if (!entry.getKey().contains("ITEM_")
                && !entry.getKey().contains("FOOD_")
                && !entry.getKey().contains("_CANDY")
                && !entry.getKey().contains("CASH_")
                && !entry.getKey().contains("EVENT_SHAPE_")
                && entry.getValue().getSkillTemplate().getActivationAttribute() != ActivationAttribute.TOGGLE) {
                entry.getValue().endEffect();
                abnormalEffectMap.remove(entry.getKey());
            }
        }
        for (Map.Entry<String, Effect> entry : noshowEffects.entrySet()) {
            if (!entry.getKey().contains("ITEM_")
                && !entry.getKey().contains("FOOD_")
                && !entry.getKey().contains("_CANDY")
                && !entry.getKey().contains("CASH_")
                && !entry.getKey().contains("EVENT_SHAPE_")
                && entry.getValue().getSkillTemplate().getActivationAttribute() != ActivationAttribute.TOGGLE) {
                entry.getValue().endEffect();
                noshowEffects.remove(entry.getKey());
            }
        }
    }

    public void updatePlayerEffectIcons() {
        getOwner().addPacketBroadcastMask(BroadcastMode.UPDATE_PLAYER_EFFECT_ICONS);
    }

    public void updatePlayerEffectIconsImpl() {
        Collection<Effect> effects = getAbnormalEffects();

        Player player = (Player) owner;

        PacketSendUtility.sendPacket(player, new SM_ABNORMAL_STATE(effects, abnormals));

        if (player.isInGroup())
            player.getPlayerGroup().updateGroupUIToEvent(player, GroupEvent.UPDATE);
        if (player.isInAlliance())
            AllianceService.getInstance().updateAllianceUIToEvent(player,
                PlayerAllianceEvent.UPDATE);
    }

    /**
     * @return copy of anbornals list
     */
    public Collection<Effect> getAbnormalEffects() {
        List<Effect> effects = new ArrayList<Effect>();

        Iterator<Effect> iterator = iterator();
        while (iterator.hasNext()) {
            Effect effect = iterator.next();
            if (effect != null)
                effects.add(effect);
        }

        iterator = noshowEffects.values().iterator();
        while (iterator.hasNext()) {
            Effect effect = iterator.next();
            if (effect != null && effect.getTargetSlot() == SkillTargetSlot.BUFF.ordinal())
                effects.add(effect);
        }

        Collections.sort(effects, new EffectComparator());

        return effects;
    }

    public int removeEffectByDispelCat(DispelCategoryType dispelCat, SkillTargetSlot targetSlot,
        int count, int dispelLevel) {
        return removeEffectByDispelCat(dispelCat, targetSlot, count, dispelLevel, 10);
    }

    /**
     * 
     * @param dispelCat
     * @param targetSlot
     * @param count
     */
    public int removeEffectByDispelCat(DispelCategoryType dispelCat, SkillTargetSlot targetSlot,
        int count, int dispelLevel, int power2) {
        int removes = 0;
        int power = power2 * count;

        // Illusion/SWoP fix
        if (count == 1 && dispelLevel == 1 && targetSlot == SkillTargetSlot.DEBUFF)
            power = 30;

        List<Effect> effects = new ArrayList<Effect>();

        effects.addAll(abnormalEffectMap.values());

        Collections.sort(effects, new EffectComparator());

        // Map<Long, Effect> effects = createSortedMap();

        // for (Effect effect : abnormalEffectMap.values())
        // effects.put(effect.getInitTime(), effect);

        for (Effect effect : effects) {
            if (power <= 0 || removes >= count)
                break;

            if (effect == null)
                continue;

            if (effect.getSkillTemplate() != null
                && effect.getSkillTemplate().getDispelLevel() >= 2) {
                if (effect.getSkillTemplate().isSpecialFear()) {
                    if (dispelCat != DispelCategoryType.ALL || dispelLevel < 2)
                        continue;
                }
                else {
                    continue;
                }
            }

            if (effect.getTargetSlot() == targetSlot.ordinal()) {
                boolean remove = false;

                switch (dispelCat) {
                    case ALL:
                        if (effect.getDispelCat() == DispelCategoryType.ALL
                            || effect.getDispelCat() == DispelCategoryType.DEBUFF_MENTAL
                            || effect.getDispelCat() == DispelCategoryType.DEBUFF_PHYSICAL)
                            remove = true;
                        break;
                    case DEBUFF_MENTAL:
                        if (effect.getDispelCat() == DispelCategoryType.ALL
                            || effect.getDispelCat() == DispelCategoryType.DEBUFF_MENTAL)
                            remove = true;
                        break;
                    case DEBUFF_PHYSICAL:
                        if (effect.getDispelCat() == DispelCategoryType.ALL
                            || effect.getDispelCat() == DispelCategoryType.DEBUFF_PHYSICAL
                            || effect.getStack().contains("ITEM_SKILL_PROC"))
                            remove = true;
                        break;
                    case BUFF:
                        if ((effect.getStack().contains("ITEM_"))
                            || effect.getStack().contains("FOOD_")
                            || effect.getStack().contains("_CANDY")
                            || effect.getStack().contains("CASH_")
                            || effect.getStack().contains("EVENT_SHAPE_"))
                            break;
                        else if (effect.getDispelCat() == DispelCategoryType.BUFF
                            && effect.getTargetSlotLevel() < 1)
                            remove = true;
                        break;
                    case STUN:
                        if (effect.getDispelCat() == DispelCategoryType.STUN)
                            remove = true;
                        break;
                }

                if (remove) {
                    int newPower = Math.max(power - effect.getEffectPower(), 0);

                    if (effect.getEffectPower() > 0 && removePower(effect, power)) {
                        effect.endEffect();
                        abnormalEffectMap.remove(effect.getStack());
                        removes++;
                        power = newPower;
                    }
                }
            }
        }

        if (removes < count && power > 0 && dispelCat == DispelCategoryType.BUFF) {
            for (Effect effect : effects) {
                if (power <= 0 || removes >= count)
                    break;

                if (!effect.isDispelBuffDebuff())
                    continue;

                int newPower = Math.max(power - effect.getEffectPower(), 0);

                if (effect.getEffectPower() > 0 && removePower(effect, power)) {
                    effect.endEffect();
                    abnormalEffectMap.remove(effect.getStack());
                    removes++;
                    power = newPower;
                }
            }
        }

        return removes;
    }

    public Collection<Effect> getRemoveEffectByDispelCat(DispelCategoryType dispelCat,
        SkillTargetSlot targetSlot, int count, int dispelLevel) {
        return getRemoveEffectByDispelCat(dispelCat, targetSlot, count, dispelLevel, 10);
    }

    public Collection<Effect> getRemoveEffectByDispelCat(DispelCategoryType dispelCat,
        SkillTargetSlot targetSlot, int count, int dispelLevel, int power2) {
        List<Effect> effectsToRemove = new ArrayList<Effect>();

        int removes = 0;
        int power = power2 * count;
        // Illusion/SWoP fix
        if (count == 1 && dispelLevel == 1 && targetSlot == SkillTargetSlot.DEBUFF)
            power = 30;

        List<Effect> effects = new ArrayList<Effect>(abnormalEffectMap.size());

        effects.addAll(abnormalEffectMap.values());

        Collections.sort(effects, new EffectComparator());

        // Map<Long, Effect> effects = createSortedMap();

        // for (Effect effect : abnormalEffectMap.values())
        // effects.put(effect.getInitTime(), effect);

        for (Effect effect : effects) {
            if (power <= 0 || removes >= count)
                break;

            if (effect.getSkillTemplate().getDispelLevel() >= 2) {
                if (effect.getSkillTemplate().isSpecialFear()) {
                    if (dispelCat != DispelCategoryType.DEBUFF_MENTAL)
                        continue;
                }
                else {
                    continue;
                }
            }

            if (effect.getTargetSlot() == targetSlot.ordinal()) {
                boolean remove = false;

                switch (dispelCat) {
                    case ALL:
                        if (effect.getDispelCat() == DispelCategoryType.ALL
                            || effect.getDispelCat() == DispelCategoryType.DEBUFF_MENTAL
                            || effect.getDispelCat() == DispelCategoryType.DEBUFF_PHYSICAL)
                            remove = true;
                        break;
                    case DEBUFF_MENTAL:
                        if (effect.getDispelCat() == DispelCategoryType.ALL
                            || effect.getDispelCat() == DispelCategoryType.DEBUFF_MENTAL)
                            remove = true;
                        break;
                    case DEBUFF_PHYSICAL:
                        if (effect.getDispelCat() == DispelCategoryType.ALL
                            || effect.getDispelCat() == DispelCategoryType.DEBUFF_PHYSICAL
                            || effect.getStack().contains("ITEM_SKILL_PROC"))
                            remove = true;
                        break;
                    case BUFF:
                        if ((effect.getStack().contains("ITEM_"))
                            || effect.getStack().contains("FOOD_")
                            || effect.getStack().contains("_CANDY")
                            || effect.getStack().contains("CASH_")
                            || effect.getStack().contains("EVENT_SHAPE_"))
                            break;
                        else if (effect.getDispelCat() == DispelCategoryType.BUFF
                            && effect.getTargetSlotLevel() < 1)
                            remove = true;
                        break;
                    case STUN:
                        if (effect.getDispelCat() == DispelCategoryType.STUN)
                            remove = true;
                        break;
                }

                if (remove) {
                    int newPower = Math.max(power - effect.getEffectPower(), 0);

                    if (effect.getEffectPower() > 0 && removePower(effect, power)) {
                        effectsToRemove.add(effect);
                        removes++;
                        power = newPower;
                    }
                }
            }
        }

        if (removes < count && power > 0 && dispelCat == DispelCategoryType.BUFF) {
            for (Effect effect : effects) {
                if (power <= 0 || removes >= count)
                    break;

                if (!effect.isDispelBuffDebuff())
                    continue;

                int newPower = Math.max(power - effect.getEffectPower(), 0);

                if (effect.getEffectPower() > 0 && removePower(effect, power)) {
                    effectsToRemove.add(effect);
                    removes++;
                    power = newPower;
                }
            }
        }

        return effectsToRemove;
    }

    /**
     * 
     * @param stack
     * @return abnormalEffectMap
     */
    public Effect getAbnormalEffect(String stack) {
        return abnormalEffectMap.get(stack);
    }

    /**
     * 
     */
    public Effect getNoshowEffect(String stack) {
        return noshowEffects.get(stack);
    }

    /**
     * ABNORMAL EFFECTS
     */

    public void setAbnormal(int mask) {
        abnormals |= mask;
    }

    public void unsetAbnormal(int mask) {
        int count = 0;
        for (Effect effect : abnormalEffectMap.values()) {
            if ((effect.getAbnormals() & mask) == mask)
                count++;
        }
        if (count <= 1)
            abnormals &= ~mask;
    }

    /**
     * Used for checking unique abnormal states
     * 
     * @param effectId
     * @return
     */
    public boolean isAbnormalSet(EffectId effectId) {
        return (abnormals & effectId.getEffectId()) == effectId.getEffectId();
    }

    /**
     * Used for compound abnormal state checks
     * 
     * @param effectId
     * @return
     */
    public boolean isAbnormalState(EffectId effectId) {
        int state = abnormals & effectId.getEffectId();
        return state > 0 && state <= effectId.getEffectId();
    }

    public int getAbnormals() {
        return abnormals;
    }

    /**
     * @return
     */
    public Iterator<Effect> iterator() {
        return abnormalEffectMap.values().iterator();
    }

    public Collection<Effect> getNoShowEffects() {
        return noshowEffects.values();
    }

    public boolean hasCantMoveAbnormal() {
        EffectId[] effectIds = { EffectId.SPIN, EffectId.ROOT, EffectId.SLEEP, EffectId.STUMBLE,
            EffectId.STUN, EffectId.STAGGER, EffectId.OPENAERIAL, EffectId.PARALYZE,
            EffectId.CANNOT_MOVE, /* EffectId.KNOCKBACK, */EffectId.PETRIFICATION };

        for (EffectId effectId : effectIds)
            if ((abnormals & effectId.getEffectId()) == effectId.getEffectId())
                return true;

        return false;
    }

    public void setNextStumble(long nextStumble) {
        this.nextStumble = nextStumble;
    }

    public long getNextStumble() {
        return nextStumble;
    }

    public void setNextStun(long nextStun) {
        this.nextStun = nextStun;
    }

    public long getNextStun() {
        return nextStun;
    }

    public void setNextStagger(long nextStagger) {
        this.nextStagger = nextStagger;
    }

    public long getNextStagger() {
        return nextStagger;
    }

    public boolean removePower(Effect effect, int power) {
        if (effect.removeEffectPower(power) <= 0)
            return true;

        return false;
    }

    private class EffectComparator implements Comparator<Effect> {
        @Override
        public int compare(Effect arg0, Effect arg1) {
            if (arg0 == null || arg1 == null)
                return 0;

            Long init0 = Long.valueOf(arg0.getInitTime());
            Long init1 = Long.valueOf(arg1.getInitTime());
            Integer skill0 = Integer.valueOf(arg0.getSkillId());
            Integer skill1 = Integer.valueOf(arg1.getSkillId());

            if (init0.equals(init1))
                return skill0.compareTo(skill1);

            return init0.compareTo(init1);
        }
    }
}

<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">
	<xs:include schemaLocation="modifiers.xsd"/>
	<xs:element name="player_titles">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="title" type="titleTemplate" maxOccurs="unbounded"/>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:complexType name="titleTemplate">
		<xs:sequence>
			<xs:element name="modifiers" type="Modifiers" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="id" type="xs:int" use="required"/>
		<xs:attribute name="race" type="xs:int"/>
	</xs:complexType>
</xs:schema>

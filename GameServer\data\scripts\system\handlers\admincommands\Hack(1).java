/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package admincommands;

import gameserver.configs.administration.AdminConfig;
import gameserver.model.gameobjects.player.Player;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.Util;
import gameserver.utils.chathandlers.AdminCommand;
import gameserver.world.World;

/**
 * <AUTHOR>
 * 
 */
public class Hack extends AdminCommand {

    public Hack() {
        super("hack");
    }

    @Override
    public void executeCommand(Player admin, String[] params) {
        if (admin.getAccessLevel() < AdminConfig.GM_LEVEL) {
            PacketSendUtility.sendMessage(admin,
                "You dont have enough rights to execute this command.");
            return;
        }

        if (params.length < 1) {
            PacketSendUtility.sendMessage(admin, "Syntax: //hack <log | diff>");
            return;
        }

        if ("log".startsWith(params[0].toLowerCase())) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //hack log <player name>");
                return;
            }

            Player player = World.getInstance().findPlayer(Util.convertName(params[1]));
            if (player == null) {
                PacketSendUtility.sendMessage(admin, "The player is not online!");
                return;
            }

            player.setAnimationLogging(!player.isAnimationLogging());

            if (player.isAnimationLogging()) {
                if (player.getAnimationLoggingTarget() != null
                    && player.getAnimationLoggingTarget() != admin)
                    PacketSendUtility.sendMessage(player.getAnimationLoggingTarget(),
                        player.getName() + " is now being logged by " + admin.getName());

                player.setAnimationLoggingTarget(admin);
                PacketSendUtility.sendMessage(admin, player.getName() + " is now being logged.");
                return;
            }
            else {
                if (player.getAnimationLoggingTarget() != null
                    && player.getAnimationLoggingTarget() != admin)
                    PacketSendUtility.sendMessage(
                        player.getAnimationLoggingTarget(),
                        player.getName() + " is no longer being logged because of "
                            + admin.getName());

                player.setAnimationLoggingTarget(null);
                PacketSendUtility.sendMessage(admin, player.getName()
                    + " is no longer being logged.");
                return;
            }
        }
        else if ("diff".startsWith(params[0].toLowerCase())) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //hack diff <player name>");
                return;
            }

            Player player = World.getInstance().findPlayer(Util.convertName(params[1]));
            if (player == null) {
                PacketSendUtility.sendMessage(admin, "The player is not online!");
                return;
            }

            player.setDiffLogging(!player.isDiffLogging());

            if (player.isDiffLogging()) {
                if (player.getDiffLoggingTarget() != null && player.getDiffLoggingTarget() != admin)
                    PacketSendUtility.sendMessage(player.getDiffLoggingTarget(), player.getName()
                        + " is now being diff logged by " + admin.getName());

                player.setDiffLoggingTarget(admin);
                if (player != admin)
                    PacketSendUtility.sendMessage(admin, player.getName()
                        + " is now being diff logged.");
                return;
            }
            else {
                if (player.getDiffLoggingTarget() != null && player.getDiffLoggingTarget() != admin)
                    PacketSendUtility.sendMessage(player.getDiffLoggingTarget(), player.getName()
                        + " is no longer being diff logged because of " + admin.getName());

                player.setDiffLoggingTarget(null);
                if (player != admin)
                    PacketSendUtility.sendMessage(admin, player.getName()
                        + " is no longer being diff logged.");
                return;
            }
        }
        else if ("packet".startsWith(params[0].toLowerCase())) {
            if (params.length < 2) {
                PacketSendUtility.sendMessage(admin, "Syntax: //hack packet <player name>");
                return;
            }

            Player player = World.getInstance().findPlayer(Util.convertName(params[1]));
            if (player == null) {
                PacketSendUtility.sendMessage(admin, "The player is not online!");
                return;
            }

            player.setPacketLogging(!player.isPacketLogging());

            if (player.isPacketLogging()) {
                if (player.getPacketLoggingTarget() != null
                    && player.getPacketLoggingTarget() != admin)
                    PacketSendUtility.sendMessage(player.getPacketLoggingTarget(), player.getName()
                        + " is now being packet logged by " + admin.getName());

                player.setPacketLoggingTarget(admin);
                if (player != admin)
                    PacketSendUtility.sendMessage(admin, player.getName()
                        + " is now being packet logged.");
                return;
            }
            else {
                if (player.getPacketLoggingTarget() != null
                    && player.getPacketLoggingTarget() != admin)
                    PacketSendUtility.sendMessage(player.getPacketLoggingTarget(), player.getName()
                        + " is no longer being packet logged because of " + admin.getName());

                player.setPacketLoggingTarget(null);
                if (player != admin)
                    PacketSendUtility.sendMessage(admin, player.getName()
                        + " is no longer being packet logged.");
                return;
            }
        }
    }
}

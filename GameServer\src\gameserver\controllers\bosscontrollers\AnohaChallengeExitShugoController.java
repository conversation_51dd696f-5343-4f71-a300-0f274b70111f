/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.ChatType;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.RequestResponseHandler;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_QUESTION_WINDOW;
import gameserver.utils.PacketSendUtility;

/**
 * <AUTHOR>
 * 
 */
public class AnohaChallengeExitShugoController extends BossController {
    public AnohaChallengeExitShugoController() {
        super(832923, true);
    }

    protected void think() {
        //
    }

    @Override
    public void onCreation() {
        getOwner().setCustomTag("Exit Challenge");
    }

    @Override
    public void onDialogRequest(Player player) {
        message(player, "I can return you from this damned place.");
        request(player);
    }

    private void request(Player player) {
        RequestResponseHandler responseHandler = new RequestResponseHandler(getOwner()) {
            @Override
            public void acceptRequest(Creature requester, final Player responder) {
                if (responder.getBattleground() != null)
                    responder.getBattleground().onLeave(responder, true, false);
            }

            @Override
            public void denyRequest(Creature requester, Player responder) {
            }
        };

        boolean requested = player.getResponseRequester().putRequest(SM_QUESTION_WINDOW.STR_CUSTOM, responseHandler);
        if (requested) {
            PacketSendUtility.sendPacket(player, new SM_QUESTION_WINDOW(SM_QUESTION_WINDOW.STR_CUSTOM, 0,
                "Do you wish to leave Anoha's Challenge?", ""));
        }
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendPacket(player, new SM_MESSAGE(getOwner().getObjectId(),
            "Assistant Recordkeeper", msg, ChatType.ALLIANCE));
    }
}
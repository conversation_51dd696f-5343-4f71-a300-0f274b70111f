/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.controllers.NpcController;
import gameserver.dataholders.DataManager;
import gameserver.model.NpcType;
import gameserver.model.Race;
import gameserver.model.TaskId;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.services.RespawnService;
import gameserver.skillengine.model.Effect;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.spawnengine.SpawnEngine;
import gameserver.taskmanager.tasks.PacketBroadcaster;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.utils.idfactory.IDFactory;
import gameserver.world.Executor;
import gameserver.world.FlagKnownList;
import gameserver.world.World;

import java.util.Arrays;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 */
public class AluruRvrBuffController extends BossController {

    private Npc flag = null;

    public AluruRvrBuffController() {
        super(Arrays.asList(856030, 856031, 856032, 856033), true);
    }

    @Override
    public void onCreation() {
        Npc owner = getOwner();
        
        owner.setCustomTag("Buff Lord");

        owner.getSpawn().getSpawnGroup().setInterval(15 * 60);
    }

    @Override
    public void onRespawn() {
        super.onRespawn();

        ThreadPoolManager.getInstance().schedule(new Runnable() {
            @Override
            public void run() {
                SpawnTemplate spawn = SpawnEngine.getInstance().addNewSpawn(
                    getOwner().getWorldId(), getOwner().getInstanceId(), 804112, getOwner().getX(),
                    getOwner().getY(), getOwner().getZ(), getOwner().getHeading(), 0, 0, true);

                flag = new Npc(IDFactory.getInstance().nextPlayerId(), new NpcController(), spawn,
                    DataManager.NPC_DATA.getNpcTemplate(804112));

                flag.setKnownlist(new FlagKnownList(flag));
                flag.setOutpostFlag(true);
                flag.setAi(null);
                flag.getObjectTemplate().setNpcType(NpcType.USEITEM);

                World world = World.getInstance();
                world.storeObject(flag);
                world.setPosition(flag, spawn.getWorldId(), getOwner().getInstanceId(),
                    spawn.getX(), spawn.getY(), spawn.getZ(), spawn.getHeading());
                world.spawn(flag);
            }
        }, 3 * 1000);
    }

    @Override
    public void scheduleRespawn() {
        int instanceId = getOwner().getInstanceId();

        if (!getOwner().getSpawn().isNoRespawn(instanceId)) {
            Future<?> respawnTask = RespawnService.scheduleRespawnTask(getOwner());
            addTask(TaskId.RESPAWN, respawnTask);
        }
    }

    @Override
    public void delete() {
        super.delete();

        if (flag != null)
            flag.getController().onDelete();
    }

    @Override
    public void onDie(Creature lastAttacker) {
        super.onDie(lastAttacker);

        if (flag != null)
            flag.getController().onDelete();

        Race winner = null;

        if (lastAttacker != null && lastAttacker.getMaster() != null)
            lastAttacker = lastAttacker.getMaster();

        if (lastAttacker instanceof Player)
            winner = ((Player) lastAttacker).getCommonData().getRace();

        if (winner == null)
            return;

        Race opposite = (winner == Race.ELYOS) ? Race.ASMODIANS : Race.ELYOS;

        switch (getOwner().getNpcId()) {
            case 856030:
                applyEffect(12086, 120 * 1000, opposite);
                applyEffect(12080, 120 * 1000, winner);
                applyEffect(12082, 180 * 1000, winner);
                break;
            case 856031:
                applyEffect(12091, 15 * 1000, opposite);
                applyEffect(12085, 300 * 1000, winner);
                applyEffect(12087, 180 * 1000, winner);
                break;
            case 856032:
                applyEffect(12089, 120 * 1000, opposite);
                applyEffect(12057, 300 * 1000, winner);
                applyEffect(12087, 180 * 1000, winner);
                break;
            case 856033:
                applyEffect(12091, 15 * 1000, opposite);
                applyEffect(12085, 300 * 1000, winner);
                applyEffect(12087, 180 * 1000, winner);
                break;
        }

        announce(winner, "RvR", "The " + (winner == Race.ELYOS ? "Elyos" : "Asmodians")
            + " have slain a buff boss for a powerful bonus!");
        announce(opposite, "RvR", "The " + (winner == Race.ELYOS ? "Elyos" : "Asmodians")
            + " have cursed you by killing a buff boss!");
    }

    private void applyEffect(int skillId, int duration, Race race) {
        SkillTemplate sTemplate = DataManager.SKILL_DATA.getSkillTemplate(skillId);

        for (Player pl : getOwner().getWorldMapInstance().getPlayers()) {
            if (race != null && pl.getCommonData().getRace() != race)
                continue;

            Effect effect = new Effect(pl, pl, sTemplate, 1, duration);
            effect.setGuaranteedSuccess(true);
            // pl.getEffectController().addEffect(effect);
            effect.initialize();
            effect.applyEffect();
            // effect.startEffect(true);
            PacketBroadcaster.getInstance().callSingle(pl);
        }
    }

    private void announce(final Race race, final String sender, final String msg) {
        getOwner().getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                if (race == null || pl.getCommonData().getRace() == race)
                    PacketSendUtility.sendSys2Message(pl, sender, msg);
                return true;
            }
        });
    }

    @Override
    protected void think() {
        //
    }
}
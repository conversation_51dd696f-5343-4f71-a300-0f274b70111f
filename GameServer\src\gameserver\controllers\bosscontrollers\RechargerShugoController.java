/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.dao.WeekliesDAO;
import gameserver.dao.WeekliesDAO.WeeklyType;
import gameserver.model.ChatType;
import gameserver.model.LegionManor;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.player.RequestResponseHandler;
import gameserver.network.aion.serverpackets.SM_MESSAGE;
import gameserver.network.aion.serverpackets.SM_QUESTION_WINDOW;
import gameserver.services.EventService;
import gameserver.services.SiegeService;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;

import com.aionemu.commons.database.dao.DAOManager;

/**
 * <AUTHOR>
 * 
 */
public class RechargerShugoController extends BossController {
    private static final int WEEKLY_ENTRIES = 1;

    public RechargerShugoController() {
        super(832935, true);
    }

    protected void think() {
        //
    }

    @Override
    public void onCreation() {
        getOwner().setCustomTag("Recharger Queue");
    }

    @Override
    public void onDialogRequest(Player player) {
        if (!player.isLegionMember() || !player.getLegionMember().isBrigadeGeneral()) {
            message(player,
                "Only the Brigade General of the Village's Legion can start the recharger queue.");
            return;
        }

        boolean isManor = false;

        for (LegionManor m : SiegeService.getInstance().getLegionManors().values()) {
            if (m.getLegionId() != player.getLegion().getLegionId())
                continue;
            if (!MathUtil.isInRange(getOwner(), m.getEntry().getX(), m.getEntry().getY(), 50))
                continue;

            isManor = true;
        }

        if (!isManor) {
            message(player, "You are not the owner of this Legion Village.");
            return;
        }
        else if (DAOManager.getDAO(WeekliesDAO.class).getWeeklyValue(
            player.getLegion().getLegionId(), WeeklyType.LEGION_RECHARGER) >= WEEKLY_ENTRIES) {
            message(player, "You have used up your weekly recharger queue usages.");
            return;
        }

        message(player, "Click Yes to start the recharger queue. You only have " + WEEKLY_ENTRIES
            + " usage" + (WEEKLY_ENTRIES > 1 ? "s" : "") + " per week.");
        request(player);
    }

    private void request(final Player player) {
        RequestResponseHandler responseHandler = new RequestResponseHandler(getOwner()) {
            @Override
            public void acceptRequest(Creature requester, Player responder) {
                if (EventService.getInstance().isRechargerQueueOn()) {
                    message(player,
                        "You cannot start the recharger queue while it is already running.");
                    return;
                }

                if (DAOManager.getDAO(WeekliesDAO.class).getWeeklyValue(
                    player.getLegion().getLegionId(), WeeklyType.LEGION_RECHARGER) >= WEEKLY_ENTRIES)
                    return;

                DAOManager.getDAO(WeekliesDAO.class).addWeeklyValue(
                    player.getLegion().getLegionId(), WeeklyType.LEGION_RECHARGER, 1);

                EventService.getInstance().startRechargerQueue();
            }

            @Override
            public void denyRequest(Creature requester, Player responder) {
            }
        };

        boolean requested = player.getResponseRequester().putRequest(SM_QUESTION_WINDOW.STR_CUSTOM,
            responseHandler);
        if (requested) {
            PacketSendUtility
                .sendPacket(
                    player,
                    new SM_QUESTION_WINDOW(
                        SM_QUESTION_WINDOW.STR_CUSTOM,
                        0,
                        "Do you wish to initiate a Recharger Queue?<br><br>Players may sign up with the command .recharger",
                        ""));
        }
    }

    private void message(Player player, String msg) {
        PacketSendUtility.sendPacket(player, new SM_MESSAGE(getOwner().getObjectId(),
            "Assistant Recordkeeper", msg, ChatType.ALLIANCE));
    }
}
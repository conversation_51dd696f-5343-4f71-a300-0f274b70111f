/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.controllers.MoveController;
import gameserver.controllers.NpcController;
import gameserver.controllers.attack.AggroInfo;
import gameserver.dataholders.DataManager;
import gameserver.geoEngine2.GeoEngine2;
import gameserver.geoEngine2.collision.CollisionResult;
import gameserver.geoEngine2.math.Vector3f;
import gameserver.model.EmotionType;
import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.VisibleObject;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.pvpevents.SpawnPosition;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_EMOTION;
import gameserver.skillengine.SkillEngine;
import gameserver.skillengine.model.Skill;
import gameserver.skillengine.model.SkillTemplate;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.World;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ScheduledFuture;

import org.apache.log4j.Logger;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public abstract class BossController extends NpcController {
    protected static Logger log = Logger.getLogger(BossController.class);

    protected List<VisibleObject> adds = new ArrayList<VisibleObject>();

    protected List<Integer> bossIds;
    protected boolean customSkills = false;
    protected ScheduledFuture<?> thinkTask = null;
    protected ConcurrentLinkedQueue<BossSkillUse> skillQueue = new ConcurrentLinkedQueue<BossSkillUse>();
    protected int randomWalkFails = 0;

    protected WaypointList waypoints = null;
    protected WaypointNode current = null;
    protected int currentIndex = -1;
    protected long nextTime = 0;

    protected long lastSkillUse = 0;

    protected BossController(int bossId) {
        this(bossId, false);
    }

    protected BossController(int bossId, boolean customSkills) {
        this(Arrays.asList(bossId), customSkills);
    }

    protected BossController(List<Integer> bossIds, boolean customSkills) {
        this.bossIds = bossIds;
        this.customSkills = customSkills;

        scheduleThink();
    }

    public void spawnAdds(int npcId, int count) {
        for (int i = 0; i < count; i++) {
            spawnAdd(npcId);
        }
    }

    public Npc spawnAdd(int npcId) {
        return spawnAdd(npcId, getRandomTarget());
    }

    public Npc spawnAdd(int npcId, Creature target) {
        return spawnAdd(npcId, target, 7);
    }

    public Npc spawnAdd(int npcId, Creature target, int randomDistance) {
        float x = getOwner().getX() + Rnd.get(-randomDistance, randomDistance);
        float y = getOwner().getY() + Rnd.get(-randomDistance, randomDistance);
        float z = getOwner().getZ() + 2f;

        return spawnAdd(npcId, target, x, y, z);
    }

    public Npc spawnAdd(int npcId, Creature target, float x, float y, float z) {
        return spawnAdd(npcId, target, x, y, z, true);
    }

    public Npc spawnAdd(int npcId, Creature target, float x, float y, float z, boolean geo) {
        int worldId = getOwner().getWorldId();

        SpawnTemplate gST = SpawnEngine.getInstance().addNewSpawn(worldId,
            getOwner().getInstanceId(), npcId, x, y,
            geo ? GeoEngine2.getInstance().getZ(worldId, x, y, z) : z, (byte) 0, 0, 0, true);

        VisibleObject result = SpawnEngine.getInstance().spawnObject(gST,
            getOwner().getInstanceId());
        if (result != null) {
            adds.add(result);

            if (result instanceof Npc) {
                if (target != null)
                    ((Npc) result).getAggroList().addHate(target, 50000);

                ((Npc) result).setNoHome(true);
                ((Npc) result).setBattleground(getOwner().getBattleground());
                ((Npc) result).setBgIndex(getOwner().getBgIndex());

                return (Npc) result;
            }
        }

        return null;
    }

    protected Npc spawn(int npcId, int worldId, int instanceId, float x, float y, float z,
        boolean geo) {
        return spawn(npcId, worldId, instanceId, x, y, z, (byte) 0, geo);
    }

    protected Npc spawn(int npcId, int worldId, int instanceId, float x, float y, float z,
        byte heading, boolean geo) {
        SpawnTemplate gST = SpawnEngine.getInstance().addNewSpawn(worldId, instanceId, npcId, x, y,
            geo ? GeoEngine2.getInstance().getZ(worldId, x, y, z) : z, heading, 0, 0, true);

        VisibleObject result = SpawnEngine.getInstance().spawnObject(gST, instanceId);
        if (result != null) {
            if (result instanceof Npc) {
                ((Npc) result).setBattleground(getOwner().getBattleground());
                ((Npc) result).setBgIndex(getOwner().getBgIndex());

                return (Npc) result;
            }
        }

        return null;
    }

    protected void scheduleThink() {
        if (thinkTask != null)
            return;

        thinkTask = ThreadPoolManager.getInstance().scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                if (getOwner() == null && thinkTask != null) {
                    thinkTask.cancel(false);
                    thinkTask = null;
                    return;
                }

                think();
                processSkillQueue();
                processWaypoints();
            }
        }, 1000, 1000);
    }

    protected abstract void think();

    protected void processSkillQueue() {
        if (skillQueue.isEmpty())
            return;
        else if (getOwner().isCasting())
            return;
        else if (!getOwner().canAttack())
            return;

        BossSkillUse skillUse = skillQueue.poll();

        if (skillUse.getTarget() != null && !skillUse.getTarget().getLifeStats().isAlreadyDead())
            skillUse.getBossSkill().use(skillUse.getTarget());
        else
            skillUse.getBossSkill().use(getRandomTarget());

        lastSkillUse = System.currentTimeMillis();
    }

    protected void processWaypoints() {
        if (!hasWaypoints())
            return;
        else if (getOwner().getWorldId() != waypoints.getMapId())
            return;

        if (!waypoints.isAggro()) {
            getOwner().setNoHome(true);
            getOwner().setAi(null);
        }
        else {
            if (getOwner().getAggroList() != null
                && getOwner().getAggroList().getMostHated() != null
                && getOwner().getTarget() != null) {
                getOwner().setNoHome(false);
                return;
            }

            getOwner().setNoHome(true);
        }

        if (current == null) {
            WaypointNode closest = null;
            int closestIndex = -1;

            double dist = 100D;

            for (int i = 0; i < waypoints.size(); i++) {
                WaypointNode pos = waypoints.get(i);

                double d = MathUtil.getDistance(getOwner(), pos.getX(), pos.getY(), pos.getZ());

                if (d < dist) {
                    dist = d;
                    closest = pos;
                    closestIndex = i;
                }
            }

            if (closest != null) {
                current = closest;
                currentIndex = closestIndex;
            }
        }

        if (current != null) {
            if (MathUtil.getDistance(getOwner(), current.getX(), current.getY(), current.getZ()) < waypoints
                .getThreshold()) {
                if (current.getPauseTime() > 0 && nextTime == 0) {
                    nextTime = System.currentTimeMillis() + current.getPauseTime();
                }
                else if (System.currentTimeMillis() > nextTime) {
                    nextTime = 0;

                    if (++currentIndex >= waypoints.size())
                        currentIndex = 0;

                    current = waypoints.get(currentIndex);
                }
            }

            MoveController mc = getOwner().getMoveController();

            if (mc.getTargetX() != current.getX() || mc.getTargetY() != current.getY()
                || mc.getTargetZ() != current.getZ()) {
                if (waypoints.isWalk())
                    getOwner().walkTo(current.getX(), current.getY(), current.getZ());
                else
                    getOwner().runTo(current.getX(), current.getY(), current.getZ());
            }
            else if (!mc.isMoving()) {
                mc.setDirectionChanged(true);

                if (waypoints.isWalk())
                    getOwner().walkTo(current.getX(), current.getY(), current.getZ(), 500);
                else
                    getOwner().runTo(current.getX(), current.getY(), current.getZ(), 500);
            }
        }
    }

    protected void queueSkill(BossSkill bossSkill, Creature target) {
        skillQueue.add(new BossSkillUse(bossSkill, target));
    }

    public List<Integer> getBossIds() {
        return bossIds;
    }

    public boolean hasCustomSkills() {
        return customSkills;
    }

    @Override
    public void delete() {
        if (thinkTask != null) {
            thinkTask.cancel(true);
            thinkTask = null;
        }

        super.delete();

        for (VisibleObject add : adds)
            add.getController().delete();
    }

    @Override
    public void onDie(Creature lastAttacker) {
        if (thinkTask != null) {
            thinkTask.cancel(true);
            thinkTask = null;
        }

        super.onDie(lastAttacker);

        for (VisibleObject add : adds)
            add.getController().delete();
    }

    @Override
    public void onRespawn() {
        scheduleThink();

        super.onRespawn();
    }

    protected Player getPriorityTarget() {
        Collection<AggroInfo> aggroInfos = getOwner().getAggroList().getList();

        List<Player> highPriority = new ArrayList<Player>();
        List<Player> mediumPriority = new ArrayList<Player>();
        List<Player> lowPriority = new ArrayList<Player>();

        for (AggroInfo aggroInfo : aggroInfos) {
            if (!(aggroInfo.getAttacker() instanceof Player))
                continue;
            else if (!getOwner().canSee((Player) aggroInfo.getAttacker()))
                continue;
            else if (!MathUtil.isInRange((Player) aggroInfo.getAttacker(), getOwner(), 40))
                continue;

            Player attacker = (Player) aggroInfo.getAttacker();

            switch (attacker.getPlayerClass()) {
                case CLERIC:
                case CHANTER:
                case BARD:
                    highPriority.add(attacker);
                    break;
                case SORCERER:
                case SPIRIT_MASTER:
                    mediumPriority.add(attacker);
                    break;
                default:
                    lowPriority.add(attacker);
                    break;
            }
        }

        if (highPriority.isEmpty()) {
            if (mediumPriority.isEmpty())
                return lowPriority.isEmpty() ? null : lowPriority.get(Rnd.get(lowPriority.size()));

            return mediumPriority.get(Rnd.get(mediumPriority.size()));
        }

        return highPriority.get(Rnd.get(highPriority.size()));
    }

    protected Creature getMostHated() {
        return getOwner().getAggroList().getMostHated();
    }

    protected Creature getRandomTarget() {
        List<AionObject> objects = new ArrayList<AionObject>(getOwner().getKnownList().getObjects());
        List<Creature> finalList = new ArrayList<Creature>();
        for (AionObject object : objects) {
            if (!(object instanceof Creature))
                continue;
            else if (!getOwner().canSee((Creature) object))
                continue;
            else if (!MathUtil.isInRange((Creature) object, getOwner(), 40))
                continue;

            finalList.add((Creature) object);
        }

        if (finalList.size() == 0)
            return null;

        return finalList.get(Rnd.get(finalList.size()));
    }

    protected void randomWalk(int radius) {
        Npc owner = (Npc) getOwner();

        if (!owner.isSpawned() || owner.getActiveRegion() == null
            || !owner.getActiveRegion().isMapRegionActive())
            return;

        float newX = owner.getSpawn().getX() + Rnd.get(-radius, radius);
        float newY = owner.getSpawn().getY() + Rnd.get(-radius, radius);

        CollisionResult result = GeoEngine2.getInstance().getClosestCollisionResult(owner, newX,
            newY, owner.getZ());

        Vector3f coll = result.getContactPoint();

        coll.setZ(GeoEngine2.getInstance().getZ(owner.getWorldId(), coll.getX(), coll.getY(),
            coll.getZ()));

        if (Math.abs(coll.getZ() - owner.getSpawn().getZ()) < 2F) {
            owner.walkTo(coll.getX(), coll.getY(), coll.getZ());
            randomWalkFails = 0;
        }
        else {
            randomWalkFails++;
        }

        if (randomWalkFails > 4) {
            World.getInstance().updatePosition(owner, owner.getSpawn().getX(),
                owner.getSpawn().getY(), owner.getSpawn().getZ(), owner.getSpawn().getHeading(),
                true);
            owner.getMoveController().stop();
            owner.getController().stopMoving();
        }
    }

    protected boolean hasWaypoints() {
        return waypoints != null && !waypoints.isEmpty();
    }

    protected class BossSkillUse {
        private BossSkill bossSkill;
        private Creature target;

        public BossSkillUse(BossSkill bossSkill, Creature target) {
            this.bossSkill = bossSkill;
            this.target = target;

            this.bossSkill.setLastUse();
        }

        public BossSkill getBossSkill() {
            return bossSkill;
        }

        public Creature getTarget() {
            return target;
        }
    }

    public class BossSkill {
        private int skillId;
        private int skillLevel;
        private long lastUse = 0;
        private SkillTemplate template;

        public BossSkill(int skillId) {
            this(skillId, 1);
        }

        public BossSkill(int skillId, int skillLevel) {
            this.setSkillId(skillId);
            this.setSkillLevel(skillLevel);

            this.template = DataManager.SKILL_DATA.getSkillTemplate(skillId);
        }

        public void use(Creature target) {
            Skill skill = SkillEngine.getInstance().getSkill(getOwner(), skillId, skillLevel,
                target);

            if (skill != null) {
                skill.useSkill();
                setLastUse();
            }
        }

        public void setSkillId(int skillId) {
            this.skillId = skillId;
        }

        public int getSkillId() {
            return skillId;
        }

        public void setSkillLevel(int skillLevel) {
            this.skillLevel = skillLevel;
        }

        public int getSkillLevel() {
            return skillLevel;
        }

        public void setLastUse() {
            int duration = 0;

            if (template != null)
                duration = template.getDuration();

            this.lastUse = System.currentTimeMillis() + duration;

            getOwner().setNextAttack(System.currentTimeMillis() + duration + 1500);
            getOwner().setNextSkill(System.currentTimeMillis() + duration + 5000);
        }

        public SkillTemplate getSkillTemplate() {
            return template;
        }

        public boolean isOnCooldown() {
            return lastUse > 0
                && (System.currentTimeMillis() - lastUse) < template.getCooldown(getSkillLevel()) * 100;
        }

        public int timeSinceUse() {
            return Math.round((float) (System.currentTimeMillis() - lastUse) / 1000);
        }
    }

    public enum WaypointMode {
        WALK_AGGRO(true, true, 2f),
        WALK_IGNORE(true, false, 2f),
        RUN_AGGRO(false, true, 7f),
        RUN_IGNORE(false, false, 7f);

        private boolean walk;
        private boolean aggro;
        private float threshold;

        private WaypointMode(boolean walk, boolean aggro, float threshold) {
            this.walk = walk;
            this.aggro = aggro;
            this.threshold = threshold;
        }

        public boolean isWalk() {
            return walk;
        }

        public boolean isAggro() {
            return aggro;
        }

        public float getThreshold() {
            return threshold;
        }
    }

    public class WaypointList {
        private int mapId;
        private WaypointMode mode;
        private List<WaypointNode> nodes = new ArrayList<WaypointNode>();

        public WaypointList(int mapId, WaypointMode mode) {
            this.mapId = mapId;
            this.mode = mode;
        }

        public void add(float x, float y, float z) {
            add(x, y, z, 0);
        }

        public void add(float x, float y, float z, int pauseTime) {
            addNode(new WaypointNode(x, y, z, pauseTime));
        }

        public void addNode(WaypointNode node) {
            this.nodes.add(node);
        }

        public void addNodes(WaypointNode... nodes) {
            this.nodes.addAll(Arrays.asList(nodes));
        }

        public void addNodes(Collection<WaypointNode> nodes) {
            this.nodes.addAll(nodes);
        }

        public WaypointNode get(int index) {
            return nodes.get(index);
        }

        public List<WaypointNode> getNodes() {
            return nodes;
        }

        public int getMapId() {
            return mapId;
        }

        public WaypointMode getMode() {
            return mode;
        }

        public int size() {
            return nodes.size();
        }

        public boolean isEmpty() {
            return nodes.isEmpty();
        }

        public boolean isAggro() {
            return mode.isAggro();
        }

        public boolean isWalk() {
            return mode.isWalk();
        }

        public float getThreshold() {
            return mode.getThreshold();
        }

        public void adjustZCoordinates() {
            for (WaypointNode node : nodes) {
                node.setZ(GeoEngine2.getInstance().getZ(mapId, node.getX(), node.getY(),
                    node.getZ()));
            }
        }
    }

    public class WaypointNode {
        private float x;
        private float y;
        private float z;
        private int pauseTime;

        public WaypointNode(SpawnPosition pos) {
            this(pos.getX(), pos.getY(), pos.getZ());
        }

        public WaypointNode(float x, float y, float z) {
            this(x, y, z, 0);
        }

        public WaypointNode(float x, float y, float z, int pauseTime) {
            this.x = x;
            this.y = y;
            this.z = z;
            this.pauseTime = pauseTime;
        }

        public float getX() {
            return x;
        }

        public float getY() {
            return y;
        }

        public float getZ() {
            return z;
        }

        public int getPauseTime() {
            return pauseTime;
        }

        public void setZ(float z) {
            this.z = z;
        }
    }
}
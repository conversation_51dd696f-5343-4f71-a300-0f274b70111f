/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers.slipperyslope;

import gameserver.ai.state.AIState;
import gameserver.controllers.bosscontrollers.BossController;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.utils.MathUtil;
import gameserver.world.World;

/**
 * <AUTHOR>
 */
public class BasaimController extends BossController {
    private final BossSkill KD = new BossSkill(18725, 1);
    
    private int shoutTicker = 0;

    public BasaimController() {
        super(281844, true);
    }

    @Override
    protected void think() {
        Npc owner = getOwner();
        SpawnTemplate spawn = owner.getSpawn();

        if (MathUtil.getDistance(owner, spawn.getX(), spawn.getY(), spawn.getZ()) > 45) {
            owner.getAi().clearDesires();
            owner.getAggroList().clearHate();

            owner.getMoveController().stop();
            World.getInstance().updatePosition(owner, spawn.getX(), spawn.getY(), spawn.getZ(),
                spawn.getHeading(), true);
            owner.getController().stopMoving();

            owner.setInCombat(false);

            owner.getAi().setAiState(AIState.ACTIVE);
            owner.getLifeStats().increaseHp(TYPE.NATURAL_HP, owner.getLifeStats().getMaxHp() / 6);
            
            return;
        }
        
        int hp = owner.getLifeStats().getHpPercentage();
        
        switch (shoutTicker) {
            case 0:
                if (hp <= 75) {
                    shoutTicker++;
                    owner.shout("Do you like the smell of feet, dwarf?");
                }
                break;
            case 1:
                if (hp <= 50) {
                    shoutTicker++;
                    owner.shout("I will stomp you flat!");
                }
                break;
        }

        Player priority = getPriorityTarget();
        if (priority == null || !MathUtil.isIn3dRange(owner, priority, 15))
            return;

        getOwner().getAggroList().addHate(priority, 10000);

        if (KD.timeSinceUse() > 20)
            queueSkill(KD, owner);
    }
}
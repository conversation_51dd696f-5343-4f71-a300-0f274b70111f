package gameserver.controllers.bosscontrollers.slipperyslope;

import gameserver.ai.state.AIState;
import gameserver.controllers.bosscontrollers.BossController;
import gameserver.controllers.bosscontrollers.BossController.BossSkill;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.utils.MathUtil;
import gameserver.world.World;

import java.util.Arrays;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class DebilkarimController extends BossController {
    private final BossSkill SLOW = new BossSkill(19300, 1);// (17853, 1);
    private final BossSkill STUN = new BossSkill(19023, 1);
    private final BossSkill MAGIC = new BossSkill(18979, 1);

    private int shoutTicker = 0;

    private int protectionTicker = 0;
    private long nextProtectionSpawn = 0;

    public DebilkarimController() {
        super(Arrays.asList(281419, 215795), true);
    }

    protected void think() {
        Npc owner = getOwner();

        if (owner.getBattleground() == null)
            return;

        SpawnTemplate spawn = owner.getSpawn();

        int hp = owner.getLifeStats().getHpPercentage();

        if (owner.getNpcId() == 281419) { // Small
            if (MathUtil.getDistance(owner, spawn.getX(), spawn.getY(), spawn.getZ()) > 35) {
                owner.getAi().clearDesires();
                owner.getAggroList().clearHate();

                owner.getMoveController().stop();
                World.getInstance().updatePosition(owner, spawn.getX(), spawn.getY(), spawn.getZ(),
                    spawn.getHeading(), true);
                owner.getController().stopMoving();

                owner.setInCombat(false);

                owner.getAi().setAiState(AIState.ACTIVE);
                return;
            }

            switch (shoutTicker) {
                case 0:
                    if (hp <= 85) {
                        shoutTicker++;
                        owner.shout("Is that all you've got?");
                    }
                    break;
                case 1:
                    if (hp <= 70) {
                        shoutTicker++;
                        owner.shout("I've seen midgets with more firepower than you!");
                    }
                    break;
                case 2:
                    if (hp <= 50) {
                        shoutTicker++;
                        owner.shout("You think you're bad, huh?");
                    }
                    break;
                case 3:
                    if (hp <= 35) {
                        shoutTicker++;
                        owner.shout("This is starting to get on my nerves...");
                    }
                    break;
            }

            if (hp <= 25 && owner.getAggroList().getMostHated() != null) {
                owner.getBattleground().onKill(owner, owner.getAggroList().getMostHated());
                owner.getController().onDelete();
            }
        }
        else { // Real deal
            if (MathUtil.getDistance(owner, spawn.getX(), spawn.getY(), spawn.getZ()) > 60) {
                owner.getAi().clearDesires();
                owner.getAggroList().clearHate();

                owner.getMoveController().stop();
                World.getInstance().updatePosition(owner, spawn.getX(), spawn.getY(), spawn.getZ(),
                    spawn.getHeading(), true);
                owner.getController().stopMoving();

                owner.setInCombat(false);

                owner.getAi().setAiState(AIState.ACTIVE);
                return;
            }

            switch (shoutTicker) {
                case 0:
                    if (hp <= 50) {
                        shoutTicker++;
                        owner.shout("Feel the wrath of the Jotun!");

                        spawnAdds(217040, 2);
                    }
                    break;
                case 1:
                    if (hp <= 25) {
                        shoutTicker++;
                        owner.shout("You think that was it? DIEEEE!");

                        // spawnAdds(215838, 1);
                    }
                    break;
            }

            if (System.currentTimeMillis() > nextProtectionSpawn) {
                switch (protectionTicker) {
                    case 0:
                        if (hp <= 90) {
                            protectionTicker++;
                            nextProtectionSpawn = System.currentTimeMillis() + 120 * 1000;

                            if (owner.getWorldId() == 300510000) {
                                spawnAdd(282469, null, 1007f, 279f, 410f);
                                spawnAdd(282469, null, 1048f, 274f, 410f);
                                spawnAdd(282469, null, 1030f, 321f, 410f);
                            }
                            else {
                                spawnAdd(282469, null, 648.24f, 2944.54f, 408f);
                                spawnAdd(282469, null, 695.08f, 2922.36f, 408f);
                                spawnAdd(282469, null, 661.18f, 2908.20f, 408f);
                            }
                        }
                        break;
                    case 1:
                        protectionTicker++;
                        nextProtectionSpawn = System.currentTimeMillis() + 90 * 1000;

                        if (owner.getWorldId() == 300510000) {
                            spawnAdd(282469, null, 1021f, 308f, 410f);
                            spawnAdd(282469, null, 1019f, 291f, 410f);
                            spawnAdd(282469, null, 1037f, 286f, 410f);
                            spawnAdd(282469, null, 1038f, 303f, 410f);
                        }
                        else {
                            spawnAdd(282469, null, 666.60f, 2947.82f, 408f);
                            spawnAdd(282469, null, 687.64f, 2939.00f, 408f);
                            spawnAdd(282469, null, 679.45f, 2917.70f, 408f);
                            spawnAdd(282469, null, 659.35f, 2927.76f, 408f);
                        }
                        break;
                    case 2:
                        protectionTicker++;
                        nextProtectionSpawn = System.currentTimeMillis() + 90 * 1000;

                        if (owner.getWorldId() == 300510000) {
                            spawnAdd(282469, null, 1048f, 297f, 410f);
                            spawnAdd(282469, null, 1013f, 298f, 410f);
                        }
                        else {
                            spawnAdd(282469, null, 669.69f, 2909.67f, 408f);
                            spawnAdd(282469, null, 656.75f, 2915.59f, 408f);
                        }
                        break;
                    default:
                        protectionTicker++;
                        nextProtectionSpawn = System.currentTimeMillis() + 60 * 1000;

                        if (owner.getWorldId() == 300510000) {
                            spawnAdd(282469, null, 1021f, 308f, 410f);
                            spawnAdd(282469, null, 1019f, 291f, 410f);
                            spawnAdd(282469, null, 1037f, 286f, 410f);
                            spawnAdd(282469, null, 1038f, 303f, 410f);
                        }
                        else {
                            spawnAdd(282469, null, 648.24f, 2944.54f, 408f);
                            spawnAdd(282469, null, 695.08f, 2922.36f, 408f);
                            spawnAdd(282469, null, 661.18f, 2908.20f, 408f);
                            spawnAdd(282469, null, 672.84f, 2933.19f, 408f);
                        }
                        break;
                }
            }

            Player priority = getPriorityTarget();
            if (priority == null || !MathUtil.isIn3dRange(owner, priority, 25))
                return;

            getOwner().getAggroList().addHate(priority, 10000);

            if (SLOW.timeSinceUse() > 40 && Rnd.get(100) < 10)
                queueSkill(SLOW, owner);
            else if (STUN.timeSinceUse() > 60 && Rnd.get(100) < 10)
                queueSkill(STUN, priority);
        }
    }
}

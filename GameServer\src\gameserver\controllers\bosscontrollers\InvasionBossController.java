/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.services.PvpService;
import gameserver.utils.MathUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class InvasionBossController extends BossController {
    private final BossSkill FEAR = new BossSkill(17887, 1);
    private final BossSkill PARALYZE = new BossSkill(17885, 1);
    private final BossSkill DOOM = new BossSkill(20987, 1);
    
    private long nextMove = 0;

    public InvasionBossController() {
        super(Arrays.asList(855152, 855172, 287258, 287259, 234162, 234164, 284259), false);
    }

    protected void think() {
        if (getOwner().getAggroList().getMostHated() != null && getOwner().getTarget() != null) {
            getOwner().setNoHome(false);
            
            if (Rnd.get(100) < 10 && FEAR.timeSinceUse() > 25)
                queueSkill(FEAR, getPriorityTarget());
            else if (Rnd.get(100) < 10 && PARALYZE.timeSinceUse() > 30)
                queueSkill(PARALYZE, getPriorityTarget());
            if (getOwner().getNpcId() == 284259 && Rnd.get(100) < 20 && DOOM.timeSinceUse() > 12)
                queueSkill(DOOM, getPriorityTarget());
            
            return;
        }
        
        getOwner().setNoHome(true);

        if (nextMove == 0) {
            nextMove = System.currentTimeMillis() + Rnd.get(14000, 20000);
        }
        else if (System.currentTimeMillis() > nextMove) {
            nextMove = 0;

            randomWalk(5);
        }
    }

    @Override
    public void doReward() {
        Npc owner = getOwner();

        List<Player> players = new ArrayList<Player>();
        players.addAll(getOwner().getKnownList().getPlayers());

        for (Iterator<Player> it = players.iterator(); it.hasNext();) {
            Player pl = it.next();

            if (!MathUtil.isIn3dRange(pl, owner, 20) || pl.getLifeStats().isAlreadyDead())
                it.remove();
        }
        
        if (players.size() == 0) {
            Player winner = getOwner().getAggroList().getMostPlayerDamage();
            
            if (winner != null) {
                PvpService.getInstance().addMight(winner, 100);
            }
            
            return;
        }

        float mightReward = 100 / players.size();

        for (Player pl : players)
            PvpService.getInstance().addMight(pl, mightReward);
    }
}

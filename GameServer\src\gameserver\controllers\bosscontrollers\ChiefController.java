/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.ai.state.AIState;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.services.OutpostService.Outpost;
import gameserver.services.PvpService;
import gameserver.utils.MathUtil;
import gameserver.utils.stats.StatFunctions;
import gameserver.world.World;

import java.util.Arrays;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class ChiefController extends BossController {
    private final BossSkill DRAGON_SLASH = new BossSkill(2704, 200);
    private Outpost outpost = null;

    public ChiefController() {
        super(Arrays.asList(231630, 231631, 231632), true);
    }

    protected void think() {
        Creature owner = getOwner();
        SpawnTemplate spawn = owner.getSpawn();

        if (outpost != null && MathUtil.getDistance(owner, spawn.getX(), spawn.getY(), spawn.getZ()) > 15) {
            owner.getAi().clearDesires();
            owner.getAggroList().clearHate();

            owner.getMoveController().stop();
            World.getInstance().updatePosition(owner, spawn.getX(), spawn.getY(), spawn.getZ(),
                spawn.getHeading(), true);
            owner.getController().stopMoving();

            owner.setInCombat(false);

            owner.getLifeStats().increaseHp(TYPE.NATURAL_HP, owner.getLifeStats().getMaxHp());
            owner.getLifeStats().increaseMp(TYPE.NATURAL_MP, owner.getLifeStats().getMaxMp());

            owner.getAi().setAiState(AIState.ACTIVE);
        }
        else {
            if (!getOwner().isGuard())
                return;

            Creature hated = getMostHated();
            if (hated != null && Rnd.get(100) < 50 && DRAGON_SLASH.timeSinceUse() > 10
                && MathUtil.isIn3dRange(getOwner(), hated, 6))
                queueSkill(DRAGON_SLASH, getMostHated());
        }
    }

    @Override
    public void doReward() {
        Player killer = getOwner().getAggroList().getMostPlayerDamage();
        if (killer != null && !killer.isOutlaw() && !killer.isLawless() && !killer.isBandit()) {
            // DP reward
            int currentDp = killer.getCommonData().getDp();
            int dpReward = 2 * StatFunctions.calculateSoloDPReward(killer, getOwner());
            killer.getCommonData().setDp(dpReward + currentDp);
            
            PvpService.getInstance().addMight(killer, 10);
            killer.getCommonData().addAp(600);
        }
    }

    public Outpost getOutpost() {
        return outpost;
    }

    public void setOutpost(Outpost outpost) {
        this.outpost = outpost;
    }
}

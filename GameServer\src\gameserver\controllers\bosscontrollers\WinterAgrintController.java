/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import java.util.TreeSet;

import gameserver.ai.state.AIState;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEffectType;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.id.StatEffectId;
import gameserver.model.gameobjects.stats.modifiers.AddModifier;
import gameserver.model.gameobjects.stats.modifiers.RateModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.services.DropService;
import gameserver.services.PvpService;
import gameserver.utils.MathUtil;
import gameserver.utils.stats.StatFunctions;
import gameserver.world.World;

/**
 * <AUTHOR>
 * 
 */
public class WinterAgrintController extends BossController {

    public WinterAgrintController() {
        super(218865, true);
    }

    @Override
    public void onCreation() {
        Npc owner = getOwner();

        owner.setCustomTag("Event");

        owner.getSpawn().getSpawnGroup().setInterval(20 * 60);

        TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

        mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, -60, true));
        mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL_RESIST, -1000, true));

        getOwner().getGameStats().endEffect(
            StatEffectId.getInstance(getOwner().getObjectId(), StatEffectType.SUMMON_EFFECT));
        getOwner().getGameStats().addModifiers(
            StatEffectId.getInstance(getOwner().getObjectId(), StatEffectType.SUMMON_EFFECT), mods);
    }

    protected void think() {
        Creature owner = getOwner();
        SpawnTemplate spawn = owner.getSpawn();

        if (MathUtil.getDistance(owner, spawn.getX(), spawn.getY(), spawn.getZ()) > 30) {
            owner.getAi().clearDesires();
            owner.getAggroList().clearHate();

            owner.getMoveController().stop();
            World.getInstance().updatePosition(owner, spawn.getX(), spawn.getY(), spawn.getZ(),
                spawn.getHeading(), true);
            owner.getController().stopMoving();

            owner.setInCombat(false);

            owner.getLifeStats().increaseHp(TYPE.NATURAL_HP, owner.getLifeStats().getMaxHp());
            owner.getLifeStats().increaseMp(TYPE.NATURAL_MP, owner.getLifeStats().getMaxMp());

            owner.getAi().setAiState(AIState.ACTIVE);
        }
    }

    @Override
    public void doReward() {
        Player killer = getOwner().getAggroList().getMostPlayerDamage();
        if (killer != null) {
            // DP reward
            int currentDp = killer.getCommonData().getDp();
            int dpReward = 2 * StatFunctions.calculateSoloDPReward(killer, getOwner());
            killer.getCommonData().setDp(dpReward + currentDp);

            PvpService.getInstance().addMight(killer, 10);
            killer.getCommonData().addAp(300);
            
            DropService.getInstance().registerDrop(getOwner(), killer, killer.getLevel());
        }
    }
}

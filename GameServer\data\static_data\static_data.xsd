<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" version="1.0">
	<xs:include schemaLocation="global_types.xsd"/>
	<xs:include schemaLocation="import.xsd"/>
	<xs:include schemaLocation="items/item_templates.xsd"/>
	<xs:include schemaLocation="stats/stats.xsd"/>
	<xs:include schemaLocation="npcs/npcs.xsd"/>
	<xs:include schemaLocation="player_experience_table.xsd"/>
	<xs:include schemaLocation="player_initial_data.xsd"/>
	<xs:include schemaLocation="world_maps.xsd"/>
	<xs:include schemaLocation="npc_trade_list.xsd"/>
	<xs:include schemaLocation="npc_teleporter.xsd"/>
	<xs:include schemaLocation="npc_walker.xsd"/>
	<xs:include schemaLocation="teleport_location.xsd"/>
	<xs:include schemaLocation="skills/skills.xsd"/>
	<xs:include schemaLocation="animations/animations_client.xsd"/>
	<xs:include schemaLocation="animations/animations_full.xsd"/>
	<xs:include schemaLocation="polish/polish_list.xsd"/>
	<xs:include schemaLocation="random_option/random_option_list.xsd"/>
	<xs:include schemaLocation="skill_tree/skill_tree.xsd"/>
	<xs:include schemaLocation="bind_points/bind_points.xsd"/>
	<xs:include schemaLocation="cube_expander/cube_expander.xsd"/>
	<xs:include schemaLocation="warehouse_expander/warehouse_expander.xsd"/>
	<xs:include schemaLocation="gatherables/gatherable_templates.xsd"/>
	<xs:include schemaLocation="quest_data/quest_data.xsd"/>
	<xs:include schemaLocation="quest_script_data/quest_script_data.xsd"/>
	<xs:include schemaLocation="zones/zones.xsd"/>
	<xs:include schemaLocation="flight_zones/flight_zones.xsd"/>
	<xs:include schemaLocation="player_titles.xsd"/>
	<xs:include schemaLocation="goodslists/goodslists.xsd"/>
	<xs:include schemaLocation="spawns/spawns.xsd"/>
	<xs:include schemaLocation="tribe/tribe_relations.xsd"/>
	<xs:include schemaLocation="recipe/recipe_templates.xsd"/>
	<xs:include schemaLocation="portals/portal_templates.xsd"/>
	<xs:include schemaLocation="item_sets/item_sets.xsd"/>
	<xs:include schemaLocation="npc_skills/npc_skills.xsd"/>
	<xs:include schemaLocation="pet_skills/pet_skills.xsd"/>
	<xs:include schemaLocation="siege/siege_locations.xsd"/>
	<xs:include schemaLocation="siege/siege_spawns.xsd"/>
	<xs:include schemaLocation="shields/shields.xsd"/>
	<xs:include schemaLocation="bonuses/bonuses.xsd"/>
	<xs:include schemaLocation="fly_rings/fly_rings.xsd"/>
	<xs:include schemaLocation="pets/pets.xsd"/>
	<xs:include schemaLocation="items/wrapped_items.xsd"/>
	<xs:include schemaLocation="compressed_item/compressed_item.xsd" />
	<xs:include schemaLocation="pvpevent_data/pvpevent_data.xsd" />
	<xs:include schemaLocation="mount/mount_data.xsd" />
	<xs:include schemaLocation="charge_skills/charge_skills.xsd" />
	<xs:include schemaLocation="safezones/safezones.xsd" />
	<xs:include schemaLocation="staticdoors/staticdoors.xsd" />
	<xs:element name="ae_static_data">
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="import" minOccurs="0" maxOccurs="unbounded"/>
				<xs:element ref="world_maps" minOccurs="0"/>
				<xs:element ref="npc_trade_list" minOccurs="0"/>
				<xs:element ref="npc_teleporter" minOccurs="0"/>
				<xs:element ref="npc_walker" minOccurs="0"/>
				<xs:element ref="teleport_location" minOccurs="0"/>
				<xs:element ref="player_experience_table" minOccurs="0"/>
				<xs:element ref="player_initial_data" minOccurs="0"/>
				<xs:element ref="item_templates" minOccurs="0"/>
				<xs:element ref="player_titles" minOccurs="0"/>
				<xs:element ref="player_stats_templates" minOccurs="0"/>
				<xs:element ref="summon_stats_templates" minOccurs="0"/>
				<xs:element ref="npc_templates" minOccurs="0"/>
				<xs:element ref="skill_data" minOccurs="0"/>
				<xs:element ref="skill_tree" minOccurs="0"/>
				<xs:element ref="bind_points" minOccurs="0"/>
				<xs:element ref="cube_expander" minOccurs="0"/>
				<xs:element ref="warehouse_expander" minOccurs="0"/>
				<xs:element ref="gatherable_templates" minOccurs="0"/>
				<xs:element ref="quests" minOccurs="0"/>
				<xs:element ref="quest_scripts" minOccurs="0"/>
				<xs:element ref="zones" minOccurs="0"/>
				<xs:element ref="flight_zones" minOccurs="0"/>
				<xs:element ref="goodslists" minOccurs="0"/>
				<xs:element ref="spawns" minOccurs="0"/>
				<xs:element ref="tribe_relations" minOccurs="0"/>
				<xs:element ref="recipe_templates" minOccurs="0"/>
				<xs:element ref="portal_templates" minOccurs="0"/>
				<xs:element ref="item_sets" minOccurs="0"/>
				<xs:element ref="npc_skill_templates" minOccurs="0"/>
				<xs:element ref="pet_skill_templates" minOccurs="0"/>
				<xs:element ref="siege_locations" minOccurs="0"/>
				<xs:element ref="siege_spawns" minOccurs="0"/>
				<xs:element ref="shields" minOccurs="0"/>
				<xs:element ref="bonuses" minOccurs="0"/>
				<xs:element ref="fly_rings" minOccurs="0"/>
				<xs:element ref="pets" minOccurs="0"/>
				<xs:element ref="compressed_items" minOccurs="0" />
				<xs:element ref="pvpevent_data" minOccurs="0" />
				<xs:element ref="wrapped_items" minOccurs="0" />
				<xs:element ref="mount_data" minOccurs="0" />
				<xs:element ref="charge_skills" minOccurs="0" />
				<xs:element ref="safezones" minOccurs="0" />
				<xs:element ref="animations_client" minOccurs="0" />
				<xs:element ref="animations_full" minOccurs="0" />
				<xs:element ref="polishes" minOccurs="0" />
				<xs:element ref="random_options" minOccurs="0" />
				<xs:element ref="staticdoors" minOccurs="0" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>

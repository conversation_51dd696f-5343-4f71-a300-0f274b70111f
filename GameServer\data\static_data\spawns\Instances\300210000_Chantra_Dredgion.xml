<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- 

	<PERSON><PERSON>

 -->
<spawns>
	<!-- Captain <PERSON> (Monsters Elite lvl:55) --> 
	<spawn map="300210000" npcid="216886" pool="1" interval="295">
		<object x="485.33307" y="832.26874" z="416.63434" h="30"/>
	</spawn>
	<!-- <PERSON><PERSON> The Inquisitor (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216889" pool="1" interval="295">
		<object x="484.1199" y="314.08817" z="403.7213" h="5"/>
	</spawn>
	<!-- Chief <PERSON><PERSON> (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216880" pool="1" interval="295">
		<object x="385.30777" y="568.3763" z="408.84164" h="73"/>
	</spawn>
	<!-- First <PERSON><PERSON> (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216879" pool="1" interval="295">
		<object x="556.88983" y="489.41568" z="393.64752" h="1"/>
	</spawn>
	<!-- Chantra Assassin (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216860" pool="12" interval="295">
		<object x="542.8912" y="267.28976" z="409.7311" h="10"/>
		<object x="566.3013" y="270.95233" z="409.95044" h="51"/>
		<object x="540.1242" y="277.08664" z="409.7311" h="119"/>
		<object x="540.3019" y="289.2601" z="409.83398" h="110"/>
		<object x="554.2" y="296.83896" z="409.78235" h="90"/>
		<object x="568.2108" y="286.81363" z="410.15076" h="64"/>
		<object x="416.388" y="297.409" z="409.842" h="87"/>
		<object x="403.275" y="283.322" z="410.121" h="2"/>
		<object x="408.61" y="267.298" z="409.773" h="23"/>
		<object x="427.702" y="265.254" z="410.094" h="55"/>
		<object x="429.839" y="279.191" z="409.731" h="57"/>
		<object x="428.186" y="291.214" z="409.731" h="69"/>
	</spawn>
	<!-- Chantra Curatus (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216869" pool="14" interval="295">
		<object x="567.17175" y="492.81128" z="394.13843" h="2"/>
		<object x="570.3886" y="487.10526" z="394.07812" h="13"/>
		<object x="578.64154" y="484.9466" z="393.7607" h="9"/>
		<object x="570.7193" y="501.3264" z="393.832" h="116"/>
		<object x="581.4009" y="499.8286" z="393.7496" h="116"/>
		<object x="539.07166" y="503.34833" z="394.0273" h="118"/>
		<object x="544.28406" y="498.70642" z="394.05618" h="118"/>
		<object x="554.5271" y="502.74207" z="393.9319" h="114"/>
		<object x="553.07416" y="516.6648" z="393.81686" h="101"/>
		<object x="550.883" y="478.49136" z="393.39532" h="108"/>
		<object x="550.75616" y="470.08478" z="393.47333" h="6"/>
		<object x="556.55426" y="466.5028" z="393.90015" h="8"/>
		<object x="565.6598" y="475.49374" z="393.38113" h="8"/>
		<object x="555.77594" y="480.1597" z="393.22922" h="12"/>
	</spawn>
	<!-- Chantra Bloodbinder (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216859" pool="6" interval="295">
		<object x="527.5562" y="493.86856" z="393.70343" h="0"/>
		<object x="508.52914" y="497.29413" z="398.1069" h="74"/>
		<object x="508.2274" y="489.47174" z="398.1259" h="39"/>
		<object x="461.22293" y="496.95074" z="398.1337" h="104"/>
		<object x="460.97742" y="490.41428" z="398.17996" h="23"/>
		<object x="441.94382" y="493.239" z="393.56778" h="61"/>
	</spawn>
	<!-- Chantra Bowguard (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216862" pool="8" interval="295">
		<object x="488.872" y="551.7466" z="393.03806" h="72"/>
		<object x="481.7015" y="552.94073" z="392.86386" h="105"/>
		<object x="479.5998" y="521.3584" z="398.2388" h="109"/>
		<object x="492.2047" y="517.77094" z="398.07208" h="81"/>
		<object x="492.58514" y="469.05374" z="398.20706" h="54"/>
		<object x="479.37305" y="468.31323" z="398.20706" h="2"/>
		<object x="481.07327" y="416.76135" z="399.369" h="89"/>
		<object x="488.69934" y="416.78226" z="399.44858" h="89"/>
	</spawn>
	<!-- Skyguard Parishka (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216887" pool="5" interval="295">
		<object x="485.45325" y="859.08984" z="417.937" h="90"/>
		<object x="497.725" y="883.25146" z="405.51166" h="70"/>
		<object x="473.43158" y="883.2824" z="405.5751" h="110"/>
		<object x="474.32172" y="869.0631" z="405.57138" h="10"/>
		<object x="496.72122" y="868.38837" z="405.56244" h="47"/>
	</spawn>
	<!-- Hookmatan (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216885" pool="2" interval="295">
		<object x="485.28537" y="877.21924" z="405.01422" h="91"/>
		<object x="342.23438" y="405.75797" z="410.67416" h="20"/>
	</spawn>
	<!-- Trigger (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216881" pool="1" interval="295">
		<object x="409.03925" y="568.88367" z="410.8066" h="53"/>
	</spawn>
	<!-- Horizonist Anuta (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216876" pool="1" interval="295">
		<object x="358.52426" y="425.95367" z="410.53293" h="75"/>
	</spawn>
	<!-- Gatekeeper Sarta (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="217037" pool="2" interval="295">
		<object x="434.1932" y="796.05164" z="413.50784" h="71"/>
		<object x="537.23254" y="794.1551" z="413.50455" h="107"/>
	</spawn>
	<!-- Quartermaster Nupakun (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216883" pool="1" interval="295">
		<object x="578.1174" y="710.38214" z="401.7698" h="92"/>
	</spawn>
	<!-- Sahadena The Abettor (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216882" pool="1" interval="295">
		<object x="505.50308" y="608.28455" z="391.57822" h="70"/>
	</spawn>
	<!-- Skylord Vundar (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216878" pool="1" interval="295">
		<object x="411.00146" y="489.09967" z="393.63916" h="117"/>
	</spawn>
	<!-- Takahan (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216884" pool="1" interval="295">
		<object x="563.973" y="706.11206" z="402.22418" h="118"/>
	</spawn>
	<!-- Chantra Legatus (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216866" pool="13" interval="295">
		<object x="385.6813" y="492.2965" z="393.99237" h="58"/>
		<object x="390.4544" y="485.10724" z="393.78445" h="83"/>
		<object x="400.15442" y="488.10303" z="394.2263" h="104"/>
		<object x="401.5842" y="498.29483" z="394.1733" h="3"/>
		<object x="392.08923" y="502.01367" z="393.551" h="38"/>
		<object x="413.89282" y="508.50372" z="393.53534" h="61"/>
		<object x="419.05496" y="500.06186" z="393.8713" h="81"/>
		<object x="429.8743" y="502.18668" z="393.7677" h="103"/>
		<object x="418.71506" y="477.53482" z="393.32816" h="13"/>
		<object x="409.93402" y="479.0794" z="393.15848" h="28"/>
		<object x="404.24" y="472.26505" z="393.53226" h="64"/>
		<object x="409.98325" y="467.6639" z="393.8196" h="85"/>
		<object x="418.83936" y="469.9167" z="393.46686" h="109"/>
	</spawn>
	<!-- Chantra Aionguard (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216864" pool="16" interval="295">
		<object x="491.89908" y="657.2925" z="388.00192" h="57"/>
		<object x="478.28186" y="657.3779" z="387.96976" h="1"/>
		<object x="478.94095" y="672.0506" z="387.93585" h="118"/>
		<object x="491.84933" y="671.98596" z="387.93576" h="63"/>
		<object x="493.1929" y="693.04694" z="387.9471" h="58"/>
		<object x="477.42334" y="692.9463" z="387.94785" h="3"/>
		<object x="473.91333" y="712.69495" z="387.6201" h="119"/>
		<object x="496.47037" y="712.4857" z="387.6183" h="60"/>
		<object x="492.05414" y="729.5223" z="387.59338" h="58"/>
		<object x="478.44727" y="729.9648" z="387.59134" h="0"/>
		<object x="489.01953" y="771.66327" z="389.12473" h="60"/>
		<object x="481.3594" y="771.4765" z="389.11914" h="119"/>
		<object x="489.2467" y="786.64154" z="387.89377" h="59"/>
		<object x="481.29263" y="786.60474" z="387.89038" h="119"/>
		<object x="489.5583" y="826.4353" z="388.08377" h="59"/>
		<object x="481.1144" y="826.7446" z="388.0863" h="118"/>
	</spawn>
	<!-- Chantra Highmagus (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216873" pool="15" interval="295">
		<object x="485.21173" y="813.6538" z="387.45505" h="89"/>
		<object x="485.1047" y="797.2658" z="387.36404" h="89"/>
		<object x="494.31952" y="741.75464" z="388.66696" h="48"/>
		<object x="476.0944" y="741.83984" z="388.6668" h="13"/>
		<object x="507.95746" y="769.31665" z="389.05472" h="95"/>
		<object x="502.02484" y="750.1255" z="389.12924" h="45"/>
		<object x="465.8075" y="766.50507" z="388.66534" h="111"/>
		<object x="540.18835" y="756.086" z="393.53314" h="55"/>
		<object x="566.7509" y="734.6433" z="400.29117" h="40"/>
		<object x="424.91757" y="753.8613" z="394.0942" h="8"/>
		<object x="406.46033" y="738.8675" z="399.20673" h="16"/>
		<object x="393.7592" y="651.2423" z="410.81195" h="25"/>
		<object x="392.3705" y="676.288" z="404.60895" h="32"/>
		<object x="576.98193" y="674.171" z="405.02127" h="25"/>
		<object x="578.4583" y="651.08295" z="410.8545" h="97"/>
	</spawn>
	<!-- Shulack Necksnapper (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216856" pool="16" interval="295">
		<object x="404.4069" y="348.69223" z="402.8995" h="77"/>
		<object x="398.95938" y="348.2937" z="403.26324" h="109"/>
		<object x="406.38525" y="302.0208" z="409.90012" h="96"/>
		<object x="407.20316" y="320.80466" z="403.08813" h="22"/>
		<object x="411.5935" y="330.5219" z="402.62128" h="78"/>
		<object x="435.53522" y="315.5592" z="404.72012" h="16"/>
		<object x="426.01343" y="301.78952" z="408.74377" h="78"/>
		<object x="426.5734" y="324.9509" z="403.17902" h="2"/>
		<object x="544.96423" y="301.18347" z="408.97916" h="99"/>
		<object x="535.07465" y="315.57938" z="404.74573" h="41"/>
		<object x="540.3808" y="325.63263" z="403.28564" h="119"/>
		<object x="555.0652" y="325.01764" z="402.50342" h="119"/>
		<object x="565.53796" y="305.36664" z="409.3637" h="83"/>
		<object x="563.66113" y="320.79797" z="403.0872" h="35"/>
		<object x="572.808" y="348.87595" z="403.15796" h="74"/>
		<object x="565.66406" y="348.62888" z="402.87778" h="107"/>
	</spawn>
	<!-- Chantra Raider (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216865" pool="7" interval="295">
		<object x="497.26038" y="329.82535" z="402.46265" h="75"/>
		<object x="473.41473" y="329.58047" z="402.32602" h="100"/>
		<object x="473.89523" y="300.48276" z="402.3039" h="11"/>
		<object x="497.44247" y="301.24316" z="402.35196" h="39"/>
		<object x="449.0954" y="321.9283" z="402.83743" h="55"/>
		<object x="520.40533" y="321.5966" z="402.8377" h="4"/>
		<object x="485.01688" y="340.51035" z="403.18127" h="90"/>
	</spawn>
	<!-- Chantra Ambusher (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216870" pool="8" interval="295">
		<object x="346.6205" y="444.4833" z="409.1726" h="75"/>
		<object x="334.7694" y="444.81744" z="409.33337" h="108"/>
		<object x="383.2766" y="397.93677" z="410.6501" h="34"/>
		<object x="389.10532" y="403.71417" z="410.11005" h="62"/>
		<object x="352.4677" y="538.3657" z="405.48196" h="106"/>
		<object x="356.72333" y="534.7815" z="404.88934" h="49"/>
		<object x="396.31174" y="585.1124" z="408.61578" h="117"/>
		<object x="404.78928" y="586.371" z="409.27182" h="58"/>
	</spawn>
	<!-- Chantra Sniper (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216872" pool="8" interval="295">
		<object x="368.3326" y="492.3841" z="394.46243" h="59"/>
		<object x="605.9619" y="492.82397" z="394.32797" h="119"/>
		<object x="617.008" y="537.6151" z="405.1799" h="67"/>
		<object x="612.05695" y="535.9419" z="405.59885" h="4"/>
		<object x="634.57654" y="446.7451" z="409.1794" h="55"/>
		<object x="627.6158" y="447.96503" z="409.06818" h="3"/>
		<object x="579.5841" y="404.64313" z="410.1115" h="118"/>
		<object x="586.18646" y="397.0008" z="410.67462" h="26"/>
	</spawn>
	<!-- Chantra Medic (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216874" pool="8" interval="295">
		<object x="595.7509" y="416.16534" z="410.73148" h="94"/>
		<object x="608.80835" y="422.64517" z="410.8065" h="101"/>
		<object x="618.9629" y="432.6672" z="410.75793" h="107"/>
		<object x="638.90533" y="423.74655" z="410.63562" h="51"/>
		<object x="638.0536" y="410.80243" z="410.7772" h="61"/>
		<object x="632.3171" y="400.7277" z="410.83002" h="24"/>
		<object x="622.3403" y="402.71304" z="410.99127" h="28"/>
		<object x="604.1347" y="400.46606" z="411.05246" h="38"/>
	</spawn>
	<!-- Chantra Patroller (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216858" pool="11" interval="295">
		<object x="620.7631" y="524.65283" z="401.78058" h="96"/>
		<object x="626.3438" y="509.92874" z="398.8715" h="95"/>
		<object x="621.8022" y="492.43668" z="395.83453" h="1"/>
		<object x="630.8535" y="464.54462" z="406.87262" h="89"/>
		<object x="630.9416" y="486.97073" z="399.18802" h="90"/>
		<object x="347.9558" y="492.1737" z="396.01852" h="60"/>
		<object x="339.608" y="459.4931" z="408.4168" h="90"/>
		<object x="338.52835" y="486.692" z="399.34735" h="27"/>
		<object x="339.6349" y="499.74677" z="398.0504" h="88"/>
		<object x="349.20718" y="525.2995" z="401.942" h="22"/>
		<object x="345.3106" y="514.4875" z="399.8384" h="23"/>
	</spawn>
	<!-- Chantra Vindicator (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216871" pool="6" interval="295">
		<object x="375.86716" y="553.1808" z="408.80392" h="42"/>
		<object x="389.43335" y="554.9973" z="409.34357" h="31"/>
		<object x="395.66077" y="566.2226" z="409.24066" h="57"/>
		<object x="380.56116" y="576.24927" z="408.87628" h="103"/>
		<object x="372.8179" y="565.0903" z="408.98578" h="107"/>
		<object x="361.80267" y="557.53345" z="408.6175" h="103"/>
	</spawn>
	<!-- Chantra Sentinel (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216854" pool="6" interval="295">
		<object x="385.726" y="636.2721" z="412.82834" h="86"/>
		<object x="388.05295" y="621.9609" z="409.98474" h="99"/>
		<object x="398.88394" y="602.5888" z="408.49747" h="91"/>
		<object x="585.2064" y="633.254" z="412.20648" h="92"/>
		<object x="580.0227" y="619.4398" z="409.21664" h="75"/>
		<object x="571.3576" y="603.0997" z="408.3656" h="86"/>
	</spawn>
	<!-- Chantra Triaris (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216867" pool="24" interval="295">
		<object x="575.3458" y="581.4413" z="408.69644" h="81"/>
		<object x="564.29034" y="583.4649" z="409.00296" h="105"/>
		<object x="599.17065" y="550.72675" z="408.63657" h="39"/>
		<object x="611.1413" y="557.06024" z="408.35382" h="39"/>
		<object x="570.278" y="557.9443" z="409.0626" h="12"/>
		<object x="595.67456" y="576.64453" z="408.77744" h="78"/>
		<object x="503.87744" y="592.35187" z="391.57822" h="44"/>
		<object x="499.94785" y="583.65674" z="391.57822" h="55"/>
		<object x="470.69348" y="582.1172" z="391.57822" h="5"/>
		<object x="465.75656" y="592.4547" z="391.57822" h="16"/>
		<object x="467.94647" y="611.49896" z="391.57822" h="116"/>
		<object x="464.26877" y="602.68256" z="391.5902" h="105"/>
		<object x="489.25803" y="619.72394" z="390.8914" h="62"/>
		<object x="481.91428" y="619.4935" z="390.891" h="114"/>
		<object x="485.1412" y="849.4094" z="393.57672" h="90"/>
		<object x="515.77686" y="866.286" z="410.4894" h="52"/>
		<object x="517.40826" y="874.5593" z="408.4166" h="57"/>
		<object x="454.42657" y="865.97644" z="410.49976" h="7"/>
		<object x="452.32013" y="874.2687" z="408.37598" h="1"/>
		<object x="494.21408" y="844.82654" z="416.52744" h="32"/>
		<object x="475.98965" y="844.2589" z="416.4626" h="27"/>
		<object x="485.41684" y="843.46735" z="416.58627" h="32"/>
		<object x="465.7173" y="820.0398" z="416.6453" h="72"/>
		<object x="505.04843" y="820.8285" z="416.64322" h="104"/>
	</spawn>
	<!-- Chantra Crusader (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216861" pool="7" interval="295">
		<object x="485.36432" y="905.70306" z="405.17743" h="92"/>
		<object x="477.97147" y="904.8475" z="405.18137" h="95"/>
		<object x="492.64774" y="905.4069" z="405.18643" h="85"/>
		<object x="499.3566" y="902.8032" z="405.1948" h="80"/>
		<object x="471.1811" y="902.28455" z="405.18964" h="100"/>
		<object x="465.66525" y="897.8092" z="405.18704" h="105"/>
		<object x="505.0249" y="898.0801" z="405.1866" h="74"/>
	</spawn>
	<!-- Chantra Trooper (Monsters Elite lvl:55) -->
	<spawn map="300210000" npcid="216857" pool="18" interval="295">
		<object x="565.7207" y="774.69293" z="412.64697" h="18"/>
		<object x="573.8808" y="786.7438" z="412.6482" h="80"/>
		<object x="588.7045" y="756.0714" z="409.16086" h="8"/>
		<object x="603.2354" y="753.4631" z="408.32214" h="64"/>
		<object x="601.68335" y="733.95264" z="405.68674" h="53"/>
		<object x="591.75885" y="737.6466" z="405.3564" h="114"/>
		<object x="411.77774" y="777.4027" z="413.07388" h="39"/>
		<object x="405.28333" y="790.28674" z="413.0835" h="98"/>
		<object x="396.38193" y="769.7015" z="411.99997" h="42"/>
		<object x="387.15698" y="782.98267" z="412.11972" h="103"/>
		<object x="370.29684" y="761.00183" z="409.38358" h="110"/>
		<object x="381.05273" y="755.3132" z="408.9312" h="51"/>
		<object x="367.88123" y="735.5316" z="405.96426" h="2"/>
		<object x="378.20032" y="736.83105" z="405.1395" h="66"/>
		<object x="387.65314" y="715.3506" z="402.2631" h="42"/>
		<object x="395.96454" y="716.7551" z="402.22354" h="20"/>
		<object x="397.5062" y="706.1081" z="402.22354" h="107"/>
		<object x="388.84778" y="704.83923" z="402.27155" h="87"/>
	</spawn>	
	<!-- Armory Maintenance Surkana 1 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700838" pool="1" interval="295">
		<object staticid="21" x="418.58847" y="281.88992" z="409.69183" h="0"/>
	</spawn>
	<!-- Armory Maintenance Surkana 2 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700839" pool="1" interval="295">
		<object staticid="30" x="553.40283" y="279.28409" z="409.74921" h="0"/>
	</spawn>
	<!-- Gravity Control Surkana Surkana 3 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700840" pool="1" interval="295">
		<object staticid="19" x="484.983" y="292.94415" z="402.19073" h="0"/>
	</spawn>
	<!-- Nuclear Control Surkana 4 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700841" pool="1" interval="295">
		<object staticid="20" x="346.1355" y="414.7247" z="410.52921" h="0"/>
	</spawn>
	<!-- Nuclear Control Surkana 5 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700842" pool="1" interval="295">
		<object staticid="28" x="625.67969" y="412.05869" z="410.48172" h="0"/>
	</spawn>
	<!-- Main Cannon Control Surkana 6 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700843" pool="1" interval="295">
		<object staticid="25" x="393.52335" y="567.06299" z="408.93356" h="0"/>
	</spawn>
	<!-- Main Cannon Control Surkana 7 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700844" pool="1" interval="295">
		<object staticid="31" x="579.93268" y="566.64429" z="408.83276" h="0"/>
	</spawn>
	<!-- Drop Device Surkana 8 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700845" pool="1" interval="295">
		<object staticid="23" x="413.06436" y="490.79669" z="393.62073" h="0"/>
	</spawn>
	<!-- Main Cannon Control Surkana 9 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700846" pool="1" interval="295">
		<object staticid="32" x="555.76239" y="491.23868" z="393.54401" h="0"/>
	</spawn>
	<!-- Fighter Enhancing Surkana 10 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700847" pool="1" interval="295">
		<object staticid="22" x="466.01926" y="607.22192" z="391.61496" h="0"/>
	</spawn>
	<!-- Storage Power Surkana 11 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700848" pool="1" interval="295">
		<object staticid="29" x="392.2063" y="710.7605" z="401.78912" h="0"/>
	</spawn>
	<!-- Storage Power Surkana 12 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700849" pool="1" interval="295">
		<object staticid="24" x="577.82098" y="711.19012" z="401.75961" h="0"/>
	</spawn>
	<!-- Bridge Power Surkana 13 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700850" pool="1" interval="295">
		<object staticid="27" x="485.00003" y="881.68243" z="404.95462" h="0"/>
	</spawn>
	<!-- Captain's Cabin Power Surkana 14 (Monsters Normal lvl:80) -->
	<spawn map="300210000" npcid="700851" pool="1" interval="295">
		<object staticid="26" x="485.34122" y="817.6004" z="416.68781" h="0"/>
	</spawn>
	<!-- Portside Defense Shield Generator Left (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730351" pool="1" interval="295">
		<object staticid="16" x="363.65765" y="571.69086" z="410.74249" h="0"/>
	</spawn>
	<!-- Starboard Defense Shield Generator Right (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730352" pool="1" interval="295">
		<object staticid="7" x="604.64386" y="570.05841" z="409.4982" h="0"/>
	</spawn>
	<!-- Portside Central Teleporter (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730311" pool="1" interval="295">
		<object staticid="9" x="554.83081" y="173.87158" z="432.52448" h="0"/>
	</spawn>
	<!-- Starboard Central Teleporter (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730312" pool="1" interval="295">
		<object staticid="42" x="397.11661" y="184.29782" z="432.80328" h="0"/>
	</spawn>
	<!-- Captain's Cabin Teleport Device (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730313" pool="1" interval="295">
		<object staticid="33" x="473.62231" y="761.99506" z="388.66" h="0"/>
	</spawn>
	<!-- Port Supply Room Teleporter Left (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730314" pool="1" interval="295">
		<object staticid="10" x="572.10443" y="185.23933" z="432.56024" h="0"/>
	</spawn>
	<!-- Starboard Supply Room Teleporter Right (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730315" pool="1" interval="295">
		<object staticid="34" x="415.07663" y="173.85265" z="432.53436" h="0"/>
	</spawn>
	<!-- Elyos Captain's Cabin Teleporter (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730357" pool="1" interval="295">
		<object staticid="186" x="496.52225" y="761.99506" z="388.66" h="0"/>
	</spawn>
	<!-- Portside Teleporter Generator Left (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730349" pool="1" interval="295">
		<object staticid="40" x="467.42523" y="586.23065" z="391.53033" h="0"/>
	</spawn>
	<!-- Starboard Teleporter Generator Right Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730350" pool="1" interval="295">
		<object staticid="41" x="503.45291" y="586.89984" z="391.51694" h="0"/>
	</spawn>
	<!-- Portside Defense Shield Left (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730345" pool="1" interval="295">
		<object staticid="12" x="448.39151" y="493.64182" z="394.13174" h="0"/>
	</spawn>
	<!-- Starboard Defense Shield Right (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730346" pool="1" interval="295">
		<object staticid="133" x="520.87555" y="493.40115" z="394.43292" h="0"/>
	</spawn>
	<!-- Port Bulkhead Left (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730353" pool="1" interval="295">
		<object staticid="95" x="445.52554" y="322.47467" z="402.4762" h="0"/>
	</spawn>
	<!-- Starboard Bulkhead Right (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="730354" pool="1" interval="295">
		<object staticid="98" x="524.67957" y="322.53738" z="402.4071" h="0"/>
	</spawn>
	<!-- Weapon Chest (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="700836" pool="12" interval="295">
		<object staticid="146" x="430.69876" y="291.90088" z="409.72235" h="0"/>
		<object staticid="149" x="407.61917" y="265.64264" z="409.66641" h="0"/>
		<object staticid="145" x="432.61469" y="278.62134" z="409.72235" h="0"/>
		<object staticid="148" x="401.19568" y="282.93854" z="410.09802" h="0"/>
		<object staticid="147" x="416.87045" y="299.0592" z="409.72235" h="0"/>	
		<object staticid="144" x="429.09814" y="264.37674" z="409.72235" h="0"/>
		<object staticid="118" x="554.5553" y="298.67249" z="409.56042" h="0"/>
		<object staticid="131" x="570.68848" y="287.48849" z="410.3638" h="0"/>
		<object staticid="134" x="568.13556" y="269.95218" z="409.87991" h="0"/>
		<object staticid="137" x="538.70697" y="290.14844" z="409.66641" h="0"/>
		<object staticid="136" x="537.89832" y="277.14233" z="409.8688" h="0"/>
		<object staticid="135" x="540.5567" y="265.74448" z="409.74835" h="10"/>
	</spawn>
	<!-- Balaur Weapon Q3721 (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="700948" pool="12" interval="295">
		<object staticid="158" x="570.59308" y="287.59137" z="411.07193" h="0"/>
		<object staticid="163" x="540.62366" y="265.88641" z="410.39618" h="10"/>
		<object staticid="159" x="537.88" y="277.133" z="410.379" h="0"/>
		<object staticid="156" x="430.47748" y="291.82193" z="410.47348" h="0"/>
		<object staticid="49" x="432.73969" y="278.62134" z="410.30725" h="0"/>
		<object staticid="91" x="428.87259" y="264.36536" z="409.71182" h="0"/>
		<object staticid="65" x="401.52963" y="283.01334" z="410.08698" h="0"/>
		<object staticid="64" x="416.80634" y="298.85071" z="410.40125" h="0"/>
		<object staticid="161" x="538.88611" y="290.03235" z="409.62915" h="0"/>
		<object staticid="155" x="407.76978" y="266.11749" z="410.38855" h="0"/>
		<object staticid="162" x="554.61279" y="298.35638" z="410.13425" h="0"/>
		<object staticid="160" x="567.64368" y="270.08945" z="409.95734" h="0"/>
	</spawn>
	<!-- Balaur Supplies Q3723 (Monsters Normal lvl:55) -->
	<spawn map="300210000" npcid="700837" pool="12" interval="295">
		<object staticid="141" x="380.33847" y="697.06158" z="402.19339" h="0"/>
		<object staticid="139" x="377.426" y="710.0556" z="402.214" h="0"/>
		<object staticid="142" x="387.53107" y="724.72626" z="403.24188" h="0"/>
		<object staticid="143" x="405.443" y="715.20154" z="402.211" h="0"/>
		<object staticid="140" x="409.04288" y="708.29089" z="403.151" h="0"/>
		<object staticid="138" x="404.14856" y="695.797" z="403.12708" h="0"/>
		<object staticid="109" x="569.249" y="695.968" z="402.208" h="0"/>
		<object staticid="15" x="562.13953" y="699.6568" z="403.151" h="0"/>
		<object staticid="93" x="562.63232" y="714.33685" z="403.12708" h="0"/>
		<object staticid="114" x="584.932" y="723.724" z="402.224" h="0"/>
		<object staticid="108" x="590.46301" y="716.15936" z="402.19888" h="0"/>
		<object staticid="6" x="589.48749" y="701.35193" z="402.19339" h="0"/>
	</spawn>
</spawns>
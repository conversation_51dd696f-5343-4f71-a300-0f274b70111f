/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.controllers.NpcController;
import gameserver.dataholders.DataManager;
import gameserver.model.NpcType;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.player.Player;
import gameserver.model.gameobjects.stats.StatEffectType;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.id.StatEffectId;
import gameserver.model.gameobjects.stats.modifiers.AddModifier;
import gameserver.model.gameobjects.stats.modifiers.RateModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.model.pvpevents.Battleground;
import gameserver.model.templates.item.EAttackType;
import gameserver.model.templates.spawn.SpawnTemplate;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.services.OpenWorldService;
import gameserver.skillengine.SkillEngine;
import gameserver.skillengine.model.Skill;
import gameserver.spawnengine.SpawnEngine;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.idfactory.IDFactory;
import gameserver.world.Executor;
import gameserver.world.FlagKnownList;
import gameserver.world.World;

import java.util.TreeSet;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class AnohaController extends BossController {
    private final BossSkill POINTFIRE1 = new BossSkill(21761, 1);
    private final BossSkill AREAFIRE1 = new BossSkill(21762, 1);
    private final BossSkill AREAFIRE2 = new BossSkill(21763, 1);
    private final BossSkill POINTFIRE2 = new BossSkill(21764, 1);
    private final BossSkill NORMALFIRE = new BossSkill(21765, 1);
    private final BossSkill AREAFIRE3 = new BossSkill(21766, 1);

    private static final int SUMMON_FIRE_SKILL = 21767;

    private static final int SKILL_QUEUE_DELAY = 8000;

    private long nextAddWave = 0;

    private Npc flag = null;

    public AnohaController() {
        super(855263, true);
    }

    @Override
    public void onCreation() {
        getOwner().getObjectTemplate().setAttackRange(30);

        TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

        mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 5000, true));
        mods.add(RateModifier.newInstance(StatEnum.MAIN_HAND_POWER, 60, true));
        mods.add(AddModifier.newInstance(StatEnum.KNOWLEDGE, 10, true));
        mods.add(AddModifier.newInstance(StatEnum.PHYSICAL_DEFENSE, -3000, true));

        getOwner().getGameStats().endEffect(
            StatEffectId.getInstance(getOwner().getObjectId(), StatEffectType.SUMMON_EFFECT));
        getOwner().getGameStats().addModifiers(
            StatEffectId.getInstance(getOwner().getObjectId(), StatEffectType.SUMMON_EFFECT), mods);
    }

    @Override
    public void onRespawn() {
        super.onRespawn();

        SpawnTemplate spawn = SpawnEngine.getInstance().addNewSpawn(getOwner().getWorldId(),
            getOwner().getInstanceId(), 702618, getOwner().getX(), getOwner().getY(),
            getOwner().getZ(), getOwner().getHeading(), 0, 0, true);

        flag = new Npc(IDFactory.getInstance().nextPlayerId(), new NpcController(), spawn,
            DataManager.NPC_DATA.getNpcTemplate(702618));

        flag.setKnownlist(new FlagKnownList(flag));
        flag.setOutpostFlag(true);
        flag.setAi(null);
        flag.getObjectTemplate().setNpcType(NpcType.USEITEM);

        World world = World.getInstance();
        world.storeObject(flag);
        world.setPosition(flag, spawn.getWorldId(), getOwner().getInstanceId(), spawn.getX(),
            spawn.getY(), spawn.getZ(), spawn.getHeading());
        world.spawn(flag);

        getOwner().getWorldMapInstance().doOnAllPlayers(new Executor<Player>() {
            @Override
            public boolean run(Player pl) {
                PacketSendUtility.sendPacket(pl, SM_SYSTEM_MESSAGE.STR_MSG_ANOHA_SPAWN());
                return true;
            }
        });
    }

    @Override
    public void onDie(Creature lastAttacker) {
        super.onDie(lastAttacker);

        getOwner().delete(2000);
    }

    @Override
    public void delete() {
        super.delete();

        if (flag != null)
            flag.getController().onDelete();
    }

    @Override
    protected void think() {
        Npc owner = getOwner();

        Player priority = getPriorityTarget();
        if (priority == null || !MathUtil.isIn3dRange(owner, priority, 45))
            return;

        getOwner().getAggroList().addHate(priority, 10000);

        if (nextAddWave == 0) {
            nextAddWave = System.currentTimeMillis() + Rnd.get(7000, 9000);
        }
        else if (nextAddWave < System.currentTimeMillis()) {
            nextAddWave = 0;

            for (int i = 0; i < 2; i++) {
                Npc add = spawnAdd(855268, null, 25);
                Skill skill = SkillEngine.getInstance().getSkill(add, SUMMON_FIRE_SKILL, 1, add);
                skill.useSkill();
                add.delete(skill.getSkillTemplate().getDuration() + 500);

                TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

                mods.add(AddModifier.newInstance(StatEnum.BOOST_MAGICAL_SKILL, 2000, true));

                add.getGameStats().endEffect(
                    StatEffectId
                        .getInstance(getOwner().getObjectId(), StatEffectType.SUMMON_EFFECT));
                add.getGameStats()
                    .addModifiers(
                        StatEffectId.getInstance(getOwner().getObjectId(),
                            StatEffectType.SUMMON_EFFECT), mods);
            }
        }

        if (System.currentTimeMillis() > super.lastSkillUse + SKILL_QUEUE_DELAY) {
            if (POINTFIRE1.timeSinceUse() > 15) {
                queueSkill(POINTFIRE1, owner);
                return;
            }

            if (POINTFIRE2.timeSinceUse() > 60) {
                queueSkill(POINTFIRE2, owner);
                queueSkill(AREAFIRE2, owner);
                return;
            }

            if (AREAFIRE3.timeSinceUse() > 40) {
                queueSkill(AREAFIRE3, owner);
                queueSkill(AREAFIRE2, owner);
                return;
            }
        }
    }
}
/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.AionObject;
import gameserver.model.gameobjects.Creature;
import gameserver.model.siege.FortressGeneral;

import java.util.Arrays;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 * 
 */
public class BalaurInvaderController extends Boss<PERSON>ontroller {
    private BossSkill ONESHOT = new BossSkill(18411, 1);
    private long skillUse = 0;

    public BalaurInvaderController() {
        super(Arrays.asList(273370, 273365, 273338), true);
        
        setSkillUse();
    }

    protected void think() {
        if (skillUse == 0 || System.currentTimeMillis() < skillUse)
            return;
        
        setSkillUse();
        
        for (AionObject obj : getOwner().getKnownList().getObjects())
            if (obj instanceof FortressGeneral)
                getOwner().getAggroList().addHate((FortressGeneral) obj, 100000);
        
        if (getOwner().getTarget() instanceof FortressGeneral) {
            queueSkill(ONESHOT, (Creature) getOwner().getTarget());
        }
    }
    
    public void setSkillUse() {
        this.skillUse = System.currentTimeMillis() + (Rnd.get(40, 80) * 1000);
    }
}

/**
 * This file is part of Aion X Emu <aionxemu.com>
 *
 *  This is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU Lesser Public License as published by
 *  the Free Software Foundation, either version 3 of the License, or
 *  (at your option) any later version.
 *
 *  This software is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU Lesser Public License for more details.
 *
 *  You should have received a copy of the GNU Lesser Public License
 *  along with this software.  If not, see <http://www.gnu.org/licenses/>.
 */

package gameserver.dao;

import gameserver.model.gameobjects.player.Player;
import gameserver.services.GloryService.PvPMode;

import com.aionemu.commons.database.dao.DAO;

/**
 * 
 * <AUTHOR>
 */

public abstract class PlayerDailiesDAO implements DAO {

    @Override
    public final String getClassName() {
        return PlayerDailiesDAO.class.getName();
    }

    public abstract int getDailyValue(Player player, PvPMode pvpMode);
    public abstract boolean addDailyValue(Player player, PvPMode pvpMode, int value);
    
    public abstract int getDailyValue(int playerObjId, PvPMode pvpMode);
    
    public abstract boolean resetPlayerDailies(Player player);
    public abstract boolean resetAllDailies();
    public abstract boolean resetCompletedDailies(PvPMode... pvpModes);
    public abstract boolean resetPlayerCompletedDailies(Player player, PvPMode... pvpModes);
}
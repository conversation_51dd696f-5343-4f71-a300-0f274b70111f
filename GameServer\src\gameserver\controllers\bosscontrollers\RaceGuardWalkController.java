/*
 *  This file is part of Zetta-Core Engine <http://www.zetta-core.org>.
 *
 *  Zetta-Core is free software: you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published
 *  by the Free Software Foundation, either version 3 of the License,
 *  or (at your option) any later version.
 *
 *  Zetta-Core is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 *  You should have received a  copy  of the GNU General Public License
 *  along with Zetta-Core.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.bosscontrollers;

import gameserver.model.gameobjects.Npc;
import gameserver.model.gameobjects.stats.StatEffectType;
import gameserver.model.gameobjects.stats.StatEnum;
import gameserver.model.gameobjects.stats.id.StatEffectId;
import gameserver.model.gameobjects.stats.modifiers.AddModifier;
import gameserver.model.gameobjects.stats.modifiers.StatModifier;
import gameserver.utils.ThreadPoolManager;
import gameserver.world.WorldMapType;

import java.util.Arrays;
import java.util.TreeSet;

/**
 * <AUTHOR>
 * 
 */
public class RaceGuardWalkController extends BossController {
    public RaceGuardWalkController() {
        super(Arrays.asList(236677, 236678, 219875, 219876, 236679, 219879, 219877, 236680), true);
    }

    @Override
    public void onRespawn() {
        super.onRespawn();

        switch (getOwner().getNpcId()) {
            case 236677:
                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        for (int i = 0; i < 2; i++) {
                            Npc add = spawnAdd(236673, null, 0);

                            if (add != null)
                                getOwner().addFollower(add);
                        }
                    }
                }, 5000);
                break;
            case 219875:
                ThreadPoolManager.getInstance().schedule(new Runnable() {
                    @Override
                    public void run() {
                        for (int i = 0; i < 2; i++) {
                            Npc add = spawnAdd(219868, null, 0);

                            if (add != null)
                                getOwner().addFollower(add);
                        }
                    }
                }, 5000);
                break;
        }
    }

    @Override
    public void onCreation() {
        switch (getOwner().getNpcId()) {
            case 236677:
                waypoints = new WaypointList(WorldMapType.CYGNEA.getId(), WaypointMode.WALK_AGGRO);

                waypoints.add(2886f, 805f, 571f, 3000);
                waypoints.add(2870f, 796f, 567f);
                waypoints.add(2846f, 795f, 563f);
                waypoints.add(2841f, 785f, 563f);
                waypoints.add(2859f, 764f, 564f);
                waypoints.add(2875f, 764f, 565f, 3000);
                waypoints.add(2915f, 779f, 563f);
                waypoints.add(2936f, 800f, 568f);
                waypoints.add(2936f, 819f, 570f);
                waypoints.add(2920f, 821f, 570f);
                waypoints.add(2904f, 812f, 570f);
                break;
            case 236678:
                waypoints = new WaypointList(WorldMapType.CYGNEA.getId(), WaypointMode.WALK_AGGRO);

                waypoints.add(2778f, 765f, 553f);
                waypoints.add(2782f, 761f, 553f);
                waypoints.add(2788f, 762f, 553f);
                waypoints.add(2800f, 779f, 558f);
                waypoints.add(2817f, 790f, 562f);
                waypoints.add(2832f, 799f, 562f);
                waypoints.add(2838f, 812f, 562f);
                waypoints.add(2842f, 825f, 562f);
                waypoints.add(2860f, 838f, 562f);
                waypoints.add(2867f, 851f, 562f);
                waypoints.add(2863f, 857f, 562f);
                waypoints.add(2852f, 840f, 562f);
                waypoints.add(2838f, 831f, 562f);
                waypoints.add(2825f, 806f, 562f);
                waypoints.add(2814f, 798f, 562f);
                waypoints.add(2796f, 784f, 558f);
                break;
            case 219875:
                waypoints = new WaypointList(WorldMapType.ENSHAR.getId(), WaypointMode.WALK_AGGRO);

                waypoints.add(458f, 2285f, 217f, 2500);
                waypoints.add(467f, 2285f, 217f);
                waypoints.add(475f, 2287f, 217f);
                waypoints.add(482f, 2294f, 217f);
                waypoints.add(486f, 2301f, 217f, 2500);
                waypoints.add(486f, 2311f, 217f);
                waypoints.add(483f, 2319f, 217f);
                waypoints.add(476f, 2325f, 217f);
                waypoints.add(469f, 2328f, 217f, 2500);
                waypoints.add(459f, 2329f, 217f);
                waypoints.add(452f, 2326f, 217f);
                waypoints.add(445f, 2320f, 217f);
                waypoints.add(442f, 2312f, 217f, 2500);
                waypoints.add(441f, 2303f, 217f);
                waypoints.add(444f, 2295f, 217f);
                waypoints.add(450f, 2289f, 217f);
                break;
            case 219876:
                waypoints = new WaypointList(WorldMapType.ENSHAR.getId(), WaypointMode.WALK_AGGRO);

                waypoints.add(497f, 2452f, 201f);
                waypoints.add(491f, 2453f, 201f);
                waypoints.add(487f, 2431f, 202f);
                waypoints.add(482f, 2406f, 212f);
                waypoints.add(478f, 2392f, 217f);
                waypoints.add(469f, 2354f, 217f);
                waypoints.add(470f, 2350f, 217f);
                waypoints.add(471f, 2348f, 217f);
                waypoints.add(474f, 2348f, 217f);
                waypoints.add(476f, 2350f, 217f);
                waypoints.add(479f, 2359f, 217f);
                waypoints.add(486f, 2392f, 217f);
                waypoints.add(490f, 2415f, 207f);
                waypoints.add(494f, 2430f, 202f);
                break;
            case 236679:
                waypoints = new WaypointList(WorldMapType.CYGNEA.getId(), WaypointMode.WALK_AGGRO);

                waypoints.add(2097, 2757, 326);
                waypoints.add(2130, 2751, 323);
                waypoints.add(2144, 2745, 325);
                waypoints.add(2165, 2746, 323);
                waypoints.add(2193, 2755, 324);
                waypoints.add(2204, 2763, 325);
                waypoints.add(2203, 2770, 325);
                waypoints.add(2197, 2784, 324);
                waypoints.add(2168, 2791, 324);
                waypoints.add(2151, 2788, 326);
                waypoints.add(2127, 2792, 325);
                waypoints.add(2106, 2776, 325);
                break;
            case 219879:
                waypoints = new WaypointList(WorldMapType.ENSHAR.getId(), WaypointMode.WALK_AGGRO);

                waypoints.add(1639, 180, 189);
                waypoints.add(1637, 183, 189);
                waypoints.add(1632, 185, 189);
                waypoints.add(1626, 184, 188);
                waypoints.add(1622, 181, 188);
                waypoints.add(1618, 175, 188);
                waypoints.add(1615, 169, 187);
                waypoints.add(1614, 160, 187);
                waypoints.add(1610, 140, 186);
                waypoints.add(1607, 135, 186);
                waypoints.add(1600, 127, 186);
                waypoints.add(1593, 122, 186);
                waypoints.add(1584, 119, 186);
                waypoints.add(1570, 118, 186);
                waypoints.add(1555, 117, 187);
                waypoints.add(1547, 115, 187);
                waypoints.add(1540, 110, 189);
                waypoints.add(1534, 105, 190);
                waypoints.add(1533, 100, 191);
                waypoints.add(1533, 96, 192);
                waypoints.add(1535, 92, 193);
                waypoints.add(1539, 89, 193);
                waypoints.add(1543, 89, 193);
                waypoints.add(1546, 89, 193);
                waypoints.add(1553, 91, 192);
                waypoints.add(1562, 93, 191);
                waypoints.add(1573, 94, 190);
                waypoints.add(1584, 96, 189);
                waypoints.add(1591, 101, 189);
                waypoints.add(1605, 111, 187);
                waypoints.add(1618, 125, 186);
                waypoints.add(1630, 136, 186);
                waypoints.add(1637, 150, 187);
                waypoints.add(1639, 165, 189);
                break;
            case 219877:
                waypoints = new WaypointList(WorldMapType.ENSHAR.getId(), WaypointMode.WALK_AGGRO);

                waypoints.add(546, 2297, 220, 5000);
                waypoints.add(544, 2310, 217);
                waypoints.add(541, 2319, 217);
                waypoints.add(538, 2320, 217);
                waypoints.add(536, 2321, 217);
                waypoints.add(534, 2320, 217);
                waypoints.add(532, 2319, 217);
                waypoints.add(530, 2317, 217);
                waypoints.add(529, 2315, 217);
                waypoints.add(529, 2313, 217);
                waypoints.add(529, 2311, 217);
                waypoints.add(531, 2307, 217);
                waypoints.add(534, 2300, 220);
                waypoints.add(535, 2294, 220);
                waypoints.add(534, 2289, 220);
                waypoints.add(529, 2275, 220);
                waypoints.add(522, 2263, 220, 5000);
                waypoints.add(525, 2261, 220);
                waypoints.add(529, 2260, 220);
                waypoints.add(531, 2261, 220);
                waypoints.add(535, 2263, 220);
                waypoints.add(536, 2266, 220);
                waypoints.add(538, 2271, 220);
                waypoints.add(541, 2279, 220);
                waypoints.add(543, 2287, 220);
                break;
            case 236680:
                waypoints = new WaypointList(WorldMapType.CYGNEA.getId(), WaypointMode.WALK_AGGRO);

                waypoints.add(2907, 857, 570, 7500);
                waypoints.add(2906, 853, 570);
                waypoints.add(2907, 850, 570);
                waypoints.add(2908, 846, 570);
                waypoints.add(2912, 839, 570);
                waypoints.add(2918, 834, 570);
                waypoints.add(2925, 830, 570);
                waypoints.add(2935, 825, 570);
                waypoints.add(2943, 820, 570);
                waypoints.add(2950, 816, 570);
                waypoints.add(2957, 815, 571);
                waypoints.add(2964, 817, 572);
                waypoints.add(2972, 819, 572);
                waypoints.add(2981, 822, 572);
                waypoints.add(2985, 826, 572);
                waypoints.add(2987, 831, 572);
                waypoints.add(2987, 834, 572);
                waypoints.add(2986, 838, 572);
                waypoints.add(2983, 841, 572);
                waypoints.add(2978, 844, 572);
                waypoints.add(2975, 846, 572);
                waypoints.add(2970, 846, 572);
                waypoints.add(2964, 844, 572, 5000);
                waypoints.add(2956, 842, 571);
                waypoints.add(2951, 840, 571);
                waypoints.add(2945, 839, 571);
                waypoints.add(2937, 840, 571);
                waypoints.add(2931, 841, 570);
                waypoints.add(2928, 843, 570);
                waypoints.add(2923, 847, 570);
                waypoints.add(2916, 852, 570);
                break;
        }

        waypoints.adjustZCoordinates();

        TreeSet<StatModifier> mods = new TreeSet<StatModifier>();

        mods.add(AddModifier.newInstance(StatEnum.ABNORMAL_RESISTANCE_ALL, -2000, true));
        mods.add(AddModifier.newInstance(StatEnum.MAGICAL_RESIST, 320, true));

        getOwner().getGameStats().endEffect(
            StatEffectId.getInstance(getOwner().getObjectId(), StatEffectType.SUMMON_EFFECT));
        getOwner().getGameStats().addModifiers(
            StatEffectId.getInstance(getOwner().getObjectId(), StatEffectType.SUMMON_EFFECT), mods);
    }

    protected void think() {

    }
}

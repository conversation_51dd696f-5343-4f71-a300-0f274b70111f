/**
 * This file is part of alpha team <alpha-team.com>.
 *
 * alpha team is pryvate software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * alpha team is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with alpha team.  If not, see <http://www.gnu.org/licenses/>.
 */
package gameserver.controllers.movement;

import gameserver.controllers.attack.AttackResult;
import gameserver.controllers.attack.AttackStatus;
import gameserver.model.gameobjects.Creature;
import gameserver.model.gameobjects.player.Player;
import gameserver.network.aion.serverpackets.SM_ATTACK_STATUS.TYPE;
import gameserver.network.aion.serverpackets.SM_SYSTEM_MESSAGE;
import gameserver.skillengine.action.DamageType;
import gameserver.skillengine.model.AttackType;
import gameserver.skillengine.model.Effect;
import gameserver.utils.MathUtil;
import gameserver.utils.PacketSendUtility;
import gameserver.utils.ThreadPoolManager;

import java.util.List;

import org.apache.log4j.Logger;

import com.aionemu.commons.utils.Rnd;

/**
 * <AUTHOR>
 */
public class AttackShieldObserver extends AttackCalcObserver {

    private int value;
    private int hit;
    private Effect effect;
    private boolean percent;
    private int probability;
    private AttackType attackType;
    private int mp;
    private int shieldType;
    private boolean isDone = false;

    /**
     * @param percent
     * @param value
     * @param status
     */
    public AttackShieldObserver(int hit, int value, boolean percent, Effect effect,
        AttackType attackType, int probability, int shieldType) {
        this(hit, value, percent, effect, attackType, probability, 0, shieldType);
    }

    /**
     * @param percent
     * @param value
     * @param status
     */
    public AttackShieldObserver(int hit, int value, boolean percent, Effect effect,
        AttackType attackType, int probability, int mp, int shieldType) {
        this.effect = effect;
        this.value = value; // totalHit for shield, radius for reflector , value for protect
        this.hit = hit;// hit for shield,reflector, range for protect
        this.percent = percent;
        this.attackType = attackType;
        this.probability = probability;
        this.mp = mp;
        this.shieldType = shieldType;
    }

    @Override
    public void checkShield(List<AttackResult> attackList, final Creature attacker,
        Effect attackEffect) {
        if (Rnd.get(100) >= probability)
            return;

        switch (attackType) {
            case SKILL:
                if (attackEffect == null)
                    return;
        }

        for (AttackResult attackResult : attackList) {
            if (attackResult.getAttackStatus().toString().contains("DODGE")
                || attackResult.getAttackStatus().toString().contains("RESIST"))
                continue;

            switch (attackType) {
                case MAGICAL_SKILL:
                    if (attackResult.getDamageType() == DamageType.PHYSICAL)
                        continue;
                    break;
                case PHYSICAL_SKILL:
                    if (attackResult.getDamageType() == DamageType.MAGICAL)
                        continue;
                    break;
                default:
                    break;
            }

            if (shieldType == 2)// shield type 2, normal shield
            {
                if (value <= 0)
                    return;

                int damage = attackResult.getDamage();

                int absorbedDamage = 0;
                if (percent)
                    absorbedDamage = damage * hit / 100;
                else
                    absorbedDamage = damage >= hit ? hit : damage;

                absorbedDamage = absorbedDamage >= value ? value : absorbedDamage;
                value -= absorbedDamage;

                if (absorbedDamage > 0)
                    attackResult.setShieldType(shieldType);
                attackResult.setDamage(damage - absorbedDamage);

                if (value <= 0 && !isDone) {
                    isDone = true;
                    effect.endEffect();
                    return;
                }
            }
            else if (shieldType == 16) // mp shield
            {
                if (value <= 0)
                    return;

                int damage = attackResult.getDamage();

                int absorbedDamage = 0;
                if (percent)
                    absorbedDamage = damage * hit / 100;
                else
                    absorbedDamage = damage >= hit ? hit : damage;

                absorbedDamage = absorbedDamage >= value ? value : absorbedDamage;
                value -= absorbedDamage;

                if (absorbedDamage > 0)
                    attackResult.setShieldType(shieldType);

                attackResult.setDamage(damage - absorbedDamage);

                if (mp > 0) {
                    int mpUse = absorbedDamage * mp / 100;

                    attackResult.setManaShieldCost(mpUse);
                    attackResult.setManaShieldSkillId(effect.getSkillId());

                    if (effect.getEffected().getLifeStats().getCurrentMp() < mpUse && !isDone) {
                        isDone = true;
                        effect.endEffect();
                        return;
                    }

                    effect.getEffected().getLifeStats().reduceMp(mpUse);
                    effect.getEffected().getLifeStats()
                        .sendAttackStatusPacketUpdate(TYPE.ABSORBED_MP, -mpUse, 0, 187);
                }

                if (value <= 0 && !isDone) {
                    isDone = true;
                    effect.endEffect();
                    return;
                }
            }
            else if (shieldType == 1)// shield type 1, reflected damage
            {
                int radius = value;

                if (MathUtil.isIn3dRange(attacker, effect.getEffected(), radius)) {
                    if (attackType == AttackType.SKILL) { // Skill Reflector
                        // [TODO]
                    }
                    else {
                        attackResult.setShieldType(shieldType);
                        attackResult.setReflectedDamage(-hit);
                        attackResult.setReflectedSkillId(effect.getSkillId());

                        if (effect.getEffected() instanceof Player)
                            PacketSendUtility.sendPacket((Player) effect.getEffected(),
                                SM_SYSTEM_MESSAGE.STR_SKILL_PROC_EFFECT_OCCURRED(effect
                                    .getSkillTemplate().getNameId()));

                        ThreadPoolManager.getInstance().schedule(new Runnable() {
                            @Override
                            public void run() {
                                attacker.getController().onAttack(effect.getEffected(), 0,
                                    TYPE.REGULAR, hit, AttackStatus.NORMALHIT, true);
                            }
                        }, 1000);
                    }

                    break; // only reflect dmg once
                }
            }
            else if (shieldType == 8)// shield type 8, protect
            {
                int range = hit;

                if (effect.getEffector().getLifeStats().isAlreadyDead()
                    || !effect.getEffector().isSpawned() || isDone) {
                    effect.endEffect();
                    break;
                }

                if (MathUtil.isIn3dRange(effect.getEffector(), effect.getEffected(), range)) {
                    int damageProtected = 0;

                    if (percent)
                        damageProtected = ((int) (attackResult.getDamage() * value * 0.01));
                    else
                        damageProtected = value;

                    int finalDamage = attackResult.getDamage() - damageProtected;

                    /*
                     * Bodyguard damage reduction
                     */
                    if (effect.getStack().equals("KN_GRANDPROTECTION")) {
                        float reduction = 1f;

                        switch (effect.getSkillStackLvl()) {
                            case 6:
                                reduction = 0.5f;
                                break;
                            case 5:
                                reduction = 0.6f;
                                break;
                            case 4:
                                reduction = 0.7f;
                                break;
                            case 3:
                                reduction = 0.75f;
                                break;
                            case 2:
                                reduction = 0.85f;
                                break;
                        }

                        damageProtected = (int) (damageProtected * reduction);
                    }

                    attackResult.setDamage((finalDamage <= 0 ? 0 : finalDamage));
                    attackResult.setShieldType(shieldType);
                    attackResult.setProtectedSkillId(effect.getSkillId());
                    attackResult.setProtectedDamage(damageProtected);
                    attackResult.setProtectorId(effect.getEffectorId());

                    effect
                        .getEffector()
                        .getController()
                        .onAttack(attacker, 0, TYPE.PROTECTDAMAGE, damageProtected,
                            AttackStatus.NORMALHIT, false);

                    if (damageProtected > 0)
                        effect.getEffector().getObserveController()
                            .notifyAttackedObservers(attacker);
                }
            }
        }
    }
}
